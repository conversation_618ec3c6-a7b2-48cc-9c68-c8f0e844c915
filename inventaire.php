<?php
/**
 * Page Inventaire Informatique - Schluter Systems
 */

require_once 'templates/components.php';

// Données d'exemple pour l'inventaire
$inventory_stats = [
    ['value' => '487', 'label' => 'Équipements Total'],
    ['value' => '156', 'label' => 'Ordinateurs'],
    ['value' => '89', 'label' => 'Téléphones'],
    ['value' => '45', 'label' => 'Imprimantes']
];

$recent_items = [
    [
        'id' => 'SCH-2025-001',
        'type' => 'Ordinateur Portable',
        'model' => 'Dell Latitude 5520',
        'user' => '<PERSON>',
        'location' => 'Bureau 201',
        'status' => 'Actif',
        'warranty' => '2026-12-15'
    ],
    [
        'id' => 'SCH-2025-002',
        'type' => 'Téléphone IP',
        'model' => 'Cisco 8841',
        'user' => '<PERSON>',
        'location' => 'Bureau 105',
        'status' => 'Actif',
        'warranty' => '2027-03-20'
    ],
    [
        'id' => 'SCH-2025-003',
        'type' => 'Imprimante',
        'model' => 'HP LaserJet Pro',
        'user' => 'Service Comptabilité',
        'location' => 'Salle 302',
        'status' => 'Maintenance',
        'warranty' => '2025-08-10'
    ]
];

// Génération de l'en-tête
renderSchlutterHeader('Inventaire Informatique');

// Navigation principale
renderSchlutterNavigation('informatique');

// Sous-navigation IT
renderSchlutterSubNavigation('informatique');

// Bannière
renderSchlutterBanner(
    'Inventaire Informatique',
    'Gestion complète des équipements et ressources informatiques'
);
?>

<main class="main-content">
    <!-- Section des statistiques -->
    <section class="stats-section">
        <?php renderStatsGrid($inventory_stats); ?>
    </section>

    <!-- Section des filtres et recherche -->
    <section class="filters-section">
        <div class="filters-container">
            <h2 class="section-title">Recherche et Filtres</h2>
            
            <div class="search-filters">
                <div class="search-bar">
                    <input type="text" id="searchInput" placeholder="Rechercher par ID, modèle, utilisateur...">
                    <button class="search-btn" onclick="searchInventory()">
                        <i class="material-icons">search</i>
                    </button>
                </div>
                
                <div class="filter-controls">
                    <select id="typeFilter" onchange="filterInventory()">
                        <option value="">Tous les types</option>
                        <option value="ordinateur">Ordinateurs</option>
                        <option value="telephone">Téléphones</option>
                        <option value="imprimante">Imprimantes</option>
                        <option value="serveur">Serveurs</option>
                    </select>
                    
                    <select id="statusFilter" onchange="filterInventory()">
                        <option value="">Tous les statuts</option>
                        <option value="actif">Actif</option>
                        <option value="maintenance">Maintenance</option>
                        <option value="retire">Retiré</option>
                        <option value="stock">En stock</option>
                    </select>
                    
                    <select id="locationFilter" onchange="filterInventory()">
                        <option value="">Tous les emplacements</option>
                        <option value="bureau">Bureaux</option>
                        <option value="salle">Salles de réunion</option>
                        <option value="stock">Stock</option>
                        <option value="datacenter">Datacenter</option>
                    </select>
                </div>
            </div>
        </div>
    </section>

    <!-- Section du tableau d'inventaire -->
    <section class="inventory-table-section">
        <div class="inventory-container">
            <div class="table-header-actions">
                <h2 class="section-title">Équipements Récents</h2>
                <div class="table-actions">
                    <button class="action-btn primary" onclick="addNewItem()">
                        <i class="material-icons">add</i>
                        <span>Ajouter Équipement</span>
                    </button>
                    <button class="action-btn secondary" onclick="exportInventory()">
                        <i class="material-icons">download</i>
                        <span>Exporter</span>
                    </button>
                </div>
            </div>
            
            <div class="inventory-table">
                <div class="table-header">
                    <div class="header-cell">ID</div>
                    <div class="header-cell">Type</div>
                    <div class="header-cell">Modèle</div>
                    <div class="header-cell">Utilisateur</div>
                    <div class="header-cell">Emplacement</div>
                    <div class="header-cell">Statut</div>
                    <div class="header-cell">Garantie</div>
                    <div class="header-cell">Actions</div>
                </div>
                
                <?php foreach ($recent_items as $item): ?>
                <div class="table-row">
                    <div class="table-cell">
                        <strong><?php echo $item['id']; ?></strong>
                    </div>
                    <div class="table-cell">
                        <div class="item-type">
                            <i class="material-icons">
                                <?php 
                                echo match($item['type']) {
                                    'Ordinateur Portable' => 'laptop',
                                    'Téléphone IP' => 'phone',
                                    'Imprimante' => 'print',
                                    default => 'devices'
                                };
                                ?>
                            </i>
                            <span><?php echo $item['type']; ?></span>
                        </div>
                    </div>
                    <div class="table-cell"><?php echo $item['model']; ?></div>
                    <div class="table-cell"><?php echo $item['user']; ?></div>
                    <div class="table-cell"><?php echo $item['location']; ?></div>
                    <div class="table-cell">
                        <span class="status-badge <?php echo strtolower($item['status']); ?>">
                            <?php echo $item['status']; ?>
                        </span>
                    </div>
                    <div class="table-cell">
                        <?php 
                        $warranty_date = new DateTime($item['warranty']);
                        $now = new DateTime();
                        $diff = $now->diff($warranty_date);
                        $is_expired = $warranty_date < $now;
                        ?>
                        <span class="warranty-date <?php echo $is_expired ? 'expired' : ''; ?>">
                            <?php echo $warranty_date->format('d/m/Y'); ?>
                        </span>
                    </div>
                    <div class="table-cell">
                        <div class="action-buttons">
                            <button class="action-btn-small view" onclick="viewItem('<?php echo $item['id']; ?>')">
                                <i class="material-icons">visibility</i>
                            </button>
                            <button class="action-btn-small edit" onclick="editItem('<?php echo $item['id']; ?>')">
                                <i class="material-icons">edit</i>
                            </button>
                            <button class="action-btn-small delete" onclick="deleteItem('<?php echo $item['id']; ?>')">
                                <i class="material-icons">delete</i>
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="table-pagination">
                <div class="pagination-info">
                    Affichage de 1 à 3 sur 487 équipements
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn" disabled>
                        <i class="material-icons">chevron_left</i>
                    </button>
                    <span class="pagination-current">1</span>
                    <button class="pagination-btn">
                        <i class="material-icons">chevron_right</i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des actions rapides -->
    <section class="quick-actions-section">
        <div class="quick-actions-container">
            <h2 class="section-title">Actions Rapides</h2>
            
            <div class="quick-actions-grid">
                <button class="quick-action-card" onclick="scanQRCode()">
                    <div class="action-icon">
                        <i class="material-icons">qr_code_scanner</i>
                    </div>
                    <h3>Scanner QR Code</h3>
                    <p>Scanner un équipement pour mise à jour rapide</p>
                </button>
                
                <button class="quick-action-card" onclick="bulkImport()">
                    <div class="action-icon">
                        <i class="material-icons">upload_file</i>
                    </div>
                    <h3>Import en Masse</h3>
                    <p>Importer plusieurs équipements via fichier CSV</p>
                </button>
                
                <button class="quick-action-card" onclick="generateLabels()">
                    <div class="action-icon">
                        <i class="material-icons">label</i>
                    </div>
                    <h3>Générer Étiquettes</h3>
                    <p>Créer des étiquettes pour nouveaux équipements</p>
                </button>
                
                <button class="quick-action-card" onclick="maintenanceSchedule()">
                    <div class="action-icon">
                        <i class="material-icons">schedule</i>
                    </div>
                    <h3>Planifier Maintenance</h3>
                    <p>Programmer la maintenance préventive</p>
                </button>
            </div>
        </div>
    </section>
</main>

<style>
/* Styles spécifiques à la page inventaire */
.filters-section {
    margin: 50px 0;
    background: var(--schluter-light-gray);
    padding: 40px 0;
}

.filters-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.search-filters {
    background: var(--schluter-white);
    padding: 30px;
    border-radius: var(--schluter-border-radius);
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.search-bar input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--schluter-light-gray);
    border-radius: var(--schluter-border-radius);
    font-size: 1rem;
    transition: var(--schluter-transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--schluter-primary);
}

.search-btn {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    border: none;
    padding: 12px 20px;
    border-radius: var(--schluter-border-radius);
    cursor: pointer;
    transition: var(--schluter-transition);
}

.search-btn:hover {
    background: var(--schluter-secondary);
}

.filter-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.filter-controls select {
    padding: 10px 12px;
    border: 2px solid var(--schluter-light-gray);
    border-radius: var(--schluter-border-radius);
    background: var(--schluter-white);
    font-size: 0.9rem;
    cursor: pointer;
}

/* Tableau d'inventaire */
.inventory-table-section {
    margin: 80px 0;
}

.inventory-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.table-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.table-actions {
    display: flex;
    gap: 15px;
}

.inventory-table {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    overflow: hidden;
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.table-header {
    display: grid;
    grid-template-columns: 120px 150px 200px 150px 150px 100px 120px 120px;
    background: var(--schluter-primary);
    color: var(--schluter-white);
    font-weight: 600;
}

.header-cell {
    padding: 15px 10px;
    text-align: center;
    font-size: 0.9rem;
}

.table-row {
    display: grid;
    grid-template-columns: 120px 150px 200px 150px 150px 100px 120px 120px;
    border-bottom: 1px solid var(--schluter-light-gray);
    transition: var(--schluter-transition);
}

.table-row:hover {
    background: #f8f9fa;
}

.table-cell {
    padding: 15px 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 0.9rem;
}

.item-type {
    display: flex;
    align-items: center;
    gap: 8px;
}

.item-type .material-icons {
    color: var(--schluter-primary);
    font-size: 18px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.actif {
    background: #E8F5E8;
    color: #2E7D32;
}

.status-badge.maintenance {
    background: #FFF3E0;
    color: #EF6C00;
}

.status-badge.retire {
    background: #FFEBEE;
    color: #C62828;
}

.warranty-date.expired {
    color: #C62828;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.action-btn-small {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: var(--schluter-transition);
}

.action-btn-small.view {
    color: var(--schluter-primary);
}

.action-btn-small.edit {
    color: #FF9800;
}

.action-btn-small.delete {
    color: #F44336;
}

.action-btn-small:hover {
    background: rgba(0,0,0,0.1);
}

.table-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: var(--schluter-white);
    border-top: 1px solid var(--schluter-light-gray);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background: none;
    border: 1px solid var(--schluter-light-gray);
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: var(--schluter-transition);
}

.pagination-btn:hover:not(:disabled) {
    background: var(--schluter-primary);
    color: var(--schluter-white);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-current {
    padding: 8px 12px;
    background: var(--schluter-primary);
    color: var(--schluter-white);
    border-radius: 4px;
    font-weight: 500;
}

/* Actions rapides */
.quick-actions-section {
    margin: 80px 0;
    background: var(--schluter-light-gray);
    padding: 60px 0;
}

.quick-actions-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.quick-action-card {
    background: var(--schluter-white);
    border: none;
    border-radius: var(--schluter-border-radius);
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--schluter-transition);
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--schluter-shadow);
}

.action-icon {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.action-icon .material-icons {
    font-size: 28px;
}

.quick-action-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--schluter-text);
}

.quick-action-card p {
    color: var(--schluter-dark-gray);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Responsive */
@media (max-width: 768px) {
    .table-header,
    .table-row {
        grid-template-columns: 1fr;
    }
    
    .header-cell,
    .table-cell {
        text-align: left;
        border-bottom: 1px solid var(--schluter-light-gray);
    }
    
    .table-header-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-actions {
        justify-content: center;
    }
}
</style>

<script>
// Fonctions JavaScript pour l'inventaire
function searchInventory() {
    const searchTerm = document.getElementById('searchInput').value;
    console.log('Recherche:', searchTerm);
    SchlutterPortal.utils.showNotification('Recherche en cours...', 'info');
}

function filterInventory() {
    const type = document.getElementById('typeFilter').value;
    const status = document.getElementById('statusFilter').value;
    const location = document.getElementById('locationFilter').value;
    console.log('Filtres:', { type, status, location });
    SchlutterPortal.utils.showNotification('Filtres appliqués', 'success');
}

function addNewItem() {
    SchlutterPortal.utils.showNotification('Ouverture du formulaire d\'ajout...', 'info');
}

function exportInventory() {
    SchlutterPortal.utils.showNotification('Export de l\'inventaire en cours...', 'info');
}

function viewItem(id) {
    SchlutterPortal.utils.showNotification(`Affichage de l'équipement ${id}`, 'info');
}

function editItem(id) {
    SchlutterPortal.utils.showNotification(`Édition de l'équipement ${id}`, 'info');
}

function deleteItem(id) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'équipement ${id} ?`)) {
        SchlutterPortal.utils.showNotification(`Équipement ${id} supprimé`, 'success');
    }
}

function scanQRCode() {
    SchlutterPortal.utils.showNotification('Ouverture du scanner QR Code...', 'info');
}

function bulkImport() {
    SchlutterPortal.utils.showNotification('Ouverture de l\'import en masse...', 'info');
}

function generateLabels() {
    SchlutterPortal.utils.showNotification('Génération des étiquettes...', 'info');
}

function maintenanceSchedule() {
    SchlutterPortal.utils.showNotification('Ouverture du planificateur de maintenance...', 'info');
}
</script>

<?php
// Pied de page
renderSchlutterFooter();
?>
