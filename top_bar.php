<?php
/**
 * Barre supérieure commune pour les pages RSE et ADV
 */

require_once 'config/schluter-config.php';

// Déterminer la page actuelle
$current_page = basename($_SERVER['PHP_SELF']);
$current_module = '';

// Déterminer le module actuel
foreach ($navigation_config as $key => $config) {
    if ($config['url'] === $current_page) {
        $current_module = $key;
        break;
    }
}
?>

<nav class="schluter-nav">
    <div class="nav-container">
        <div class="nav-brand">
            <img src="images/schluter-logo.png" alt="Schluter Systems" class="logo">
            <span class="brand-text"><?php echo COMPANY_NAME; ?></span>
        </div>
        
        <ul class="nav-menu">
            <?php foreach ($navigation_config as $key => $item): ?>
                <?php $active_class = ($current_module === $key) ? ' active' : ''; ?>
                <li class="nav-item<?php echo $active_class; ?>">
                    <a href="<?php echo $item['url']; ?>" class="nav-link">
                        <i class="material-icons"><?php echo $item['icon']; ?></i>
                        <span><?php echo $item['title']; ?></span>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
        
        <div class="nav-user">
            <span class="user-name">Utilisateur</span>
            <i class="material-icons">account_circle</i>
        </div>
    </div>
</nav>

<style>
/* Styles pour la barre de navigation commune */
.schluter-nav {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    height: 40px;
    width: auto;
}

.brand-text {
    font-weight: 600;
    font-size: 1.2rem;
    color: #E85A2B;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: #333;
    padding: 10px 15px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background-color: #E85A2B;
    color: white;
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #666;
}

.user-name {
    font-weight: 500;
}

/* Bannière commune */
.schluter-banner {
    background: linear-gradient(135deg, #E85A2B, #2B4C85);
    color: white;
    padding: 60px 0;
    text-align: center;
}

.banner-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.banner-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Responsive */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
    }
    
    .nav-menu {
        margin-top: 15px;
        flex-wrap: wrap;
        justify-content: center;
        gap: 15px;
    }
    
    .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    .banner-title {
        font-size: 2rem;
    }
    
    .banner-subtitle {
        font-size: 1rem;
    }
}
</style>
