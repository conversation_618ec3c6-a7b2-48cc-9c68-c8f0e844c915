<?php
/**
 * Page Informatique du portail intranet Schluter Systems
 */

require_once 'templates/components.php';

// Statistiques IT
$it_stats = [
    ['value' => '500+', 'label' => 'Équipements'],
    ['value' => '25', 'label' => 'Serveurs'],
    ['value' => '99.9%', 'label' => 'Uptime'],
    ['value' => '150', 'label' => 'Utilisateurs']
];

// Génération de l'en-tête avec Chart.js pour les graphiques
renderSchlutterHeader('Portail Informatique', [], ['https://cdn.jsdelivr.net/npm/chart.js']);

// Navigation principale
renderSchlutterNavigation('informatique');

// Sous-navigation IT
renderSchlutterSubNavigation('informatique');

// Bannière IT
renderSchlutterBanner(
    'Portail Informatique',
    'Gestion centralisée des ressources informatiques et support technique'
);
?>

<main class="main-content">
    <!-- Section des statistiques IT -->
    <section class="stats-section">
        <?php renderStatsGrid($it_stats); ?>
    </section>

    <!-- Section des services IT -->
    <section class="services-section">
        <div class="services-grid">
            <?php
            renderServiceCard(
                'Inventaire Informatique',
                'Gestion complète de l\'inventaire des équipements informatiques, suivi des licences et maintenance.',
                'inventaire.php',
                'inventory'
            );
            
            renderServiceCard(
                'Accusés de Réception',
                'Suivi des livraisons d\'équipements, validation des réceptions et gestion des bons de commande.',
                'accuse_reception.php',
                'receipt'
            );
            
            renderServiceCard(
                'Changements de Poste',
                'Gestion des fins de vie d\'équipements, migrations et renouvellements de matériel.',
                'End_Of_Life.php',
                'refresh'
            );
            
            renderServiceCard(
                'Support Technique',
                'Centre de support IT, tickets d\'incident et demandes d\'assistance technique.',
                'support_technique.php',
                'build'
            );
            
            renderServiceCard(
                'Monitoring Réseau',
                'Surveillance en temps réel de l\'infrastructure réseau et des performances système.',
                'monitoring.php',
                'network_check'
            );
            
            renderServiceCard(
                'Sécurité IT',
                'Gestion de la sécurité informatique, politiques de sécurité et audits de conformité.',
                'securite_it.php',
                'security'
            );
            ?>
        </div>
    </section>

    <!-- Section des graphiques de performance -->
    <section class="charts-section">
        <div class="charts-container">
            <h2 class="section-title">Tableaux de Bord IT</h2>
            
            <div class="charts-grid">
                <div class="chart-card">
                    <h3 class="chart-title">Utilisation des Serveurs</h3>
                    <canvas id="serverUsageChart"></canvas>
                </div>
                
                <div class="chart-card">
                    <h3 class="chart-title">Tickets de Support</h3>
                    <canvas id="supportTicketsChart"></canvas>
                </div>
                
                <div class="chart-card">
                    <h3 class="chart-title">Inventaire par Catégorie</h3>
                    <canvas id="inventoryChart"></canvas>
                </div>
                
                <div class="chart-card">
                    <h3 class="chart-title">Performance Réseau</h3>
                    <canvas id="networkChart"></canvas>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des alertes IT -->
    <section class="alerts-section">
        <div class="alerts-container">
            <h2 class="section-title">Alertes et Notifications</h2>
            
            <div class="alerts-grid">
                <div class="alert-card success">
                    <div class="alert-icon">
                        <i class="material-icons">check_circle</i>
                    </div>
                    <div class="alert-content">
                        <h4>Sauvegarde Réussie</h4>
                        <p>Sauvegarde automatique terminée avec succès à 02:00</p>
                        <span class="alert-time">Il y a 6 heures</span>
                    </div>
                </div>
                
                <div class="alert-card warning">
                    <div class="alert-icon">
                        <i class="material-icons">warning</i>
                    </div>
                    <div class="alert-content">
                        <h4>Espace Disque Faible</h4>
                        <p>Serveur SRV-02: 85% d'utilisation disque</p>
                        <span class="alert-time">Il y a 2 heures</span>
                    </div>
                </div>
                
                <div class="alert-card info">
                    <div class="alert-icon">
                        <i class="material-icons">info</i>
                    </div>
                    <div class="alert-content">
                        <h4>Maintenance Programmée</h4>
                        <p>Mise à jour système prévue dimanche 18/06 à 22:00</p>
                        <span class="alert-time">Planifié</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des actions rapides -->
    <section class="quick-actions-section">
        <div class="quick-actions-container">
            <h2 class="section-title">Actions Rapides</h2>
            
            <div class="quick-actions-grid">
                <button class="action-btn primary" onclick="openTicketModal()">
                    <i class="material-icons">add</i>
                    <span>Nouveau Ticket</span>
                </button>
                
                <button class="action-btn secondary" onclick="refreshDashboard()">
                    <i class="material-icons">refresh</i>
                    <span>Actualiser</span>
                </button>
                
                <button class="action-btn accent" onclick="exportReport()">
                    <i class="material-icons">download</i>
                    <span>Exporter Rapport</span>
                </button>
                
                <button class="action-btn info" onclick="viewLogs()">
                    <i class="material-icons">list</i>
                    <span>Voir les Logs</span>
                </button>
            </div>
        </div>
    </section>
</main>

<style>
/* Styles spécifiques à la page IT */
.charts-section {
    margin: 80px 0;
}

.charts-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

.chart-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 25px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--schluter-text);
    text-align: center;
}

/* Alertes */
.alerts-section {
    margin: 80px 0;
    background: var(--schluter-light-gray);
    padding: 60px 0;
}

.alerts-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.alerts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.alert-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
    box-shadow: 0 2px 10px var(--schluter-shadow);
    border-left: 4px solid;
}

.alert-card.success {
    border-left-color: #4CAF50;
}

.alert-card.warning {
    border-left-color: #FF9800;
}

.alert-card.info {
    border-left-color: #2196F3;
}

.alert-icon .material-icons {
    font-size: 1.5rem;
}

.alert-card.success .alert-icon {
    color: #4CAF50;
}

.alert-card.warning .alert-icon {
    color: #FF9800;
}

.alert-card.info .alert-icon {
    color: #2196F3;
}

.alert-content h4 {
    margin-bottom: 8px;
    font-weight: 600;
}

.alert-content p {
    color: var(--schluter-dark-gray);
    margin-bottom: 8px;
}

.alert-time {
    font-size: 0.8rem;
    color: var(--schluter-dark-gray);
    font-style: italic;
}

/* Actions rapides */
.quick-actions-section {
    margin: 80px 0;
}

.quick-actions-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 25px 20px;
    border: none;
    border-radius: var(--schluter-border-radius);
    cursor: pointer;
    transition: var(--schluter-transition);
    font-weight: 500;
    box-shadow: 0 2px 10px var(--schluter-shadow);
}

.action-btn.primary {
    background: var(--schluter-primary);
    color: var(--schluter-white);
}

.action-btn.secondary {
    background: var(--schluter-secondary);
    color: var(--schluter-white);
}

.action-btn.accent {
    background: var(--schluter-accent);
    color: var(--schluter-white);
}

.action-btn.info {
    background: #2196F3;
    color: var(--schluter-white);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--schluter-shadow);
}
</style>

<script>
// Configuration des graphiques Chart.js
document.addEventListener('DOMContentLoaded', function() {
    // Graphique d'utilisation des serveurs
    const serverCtx = document.getElementById('serverUsageChart').getContext('2d');
    new Chart(serverCtx, {
        type: 'doughnut',
        data: {
            labels: ['Utilisé', 'Disponible'],
            datasets: [{
                data: [75, 25],
                backgroundColor: ['#E85A2B', '#E9ECEF']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Graphique des tickets de support
    const ticketsCtx = document.getElementById('supportTicketsChart').getContext('2d');
    new Chart(ticketsCtx, {
        type: 'line',
        data: {
            labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven'],
            datasets: [{
                label: 'Tickets',
                data: [12, 8, 15, 10, 6],
                borderColor: '#2B4C85',
                backgroundColor: 'rgba(43, 76, 133, 0.1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Graphique de l'inventaire
    const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');
    new Chart(inventoryCtx, {
        type: 'bar',
        data: {
            labels: ['PC', 'Serveurs', 'Imprimantes', 'Téléphones'],
            datasets: [{
                data: [150, 25, 45, 80],
                backgroundColor: ['#E85A2B', '#2B4C85', '#F4A261', '#4CAF50']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // Graphique de performance réseau
    const networkCtx = document.getElementById('networkChart').getContext('2d');
    new Chart(networkCtx, {
        type: 'line',
        data: {
            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
            datasets: [{
                label: 'Bande passante (%)',
                data: [20, 15, 60, 80, 75, 40],
                borderColor: '#4CAF50',
                backgroundColor: 'rgba(76, 175, 80, 0.1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});

// Fonctions pour les actions rapides
function openTicketModal() {
    SchlutterPortal.utils.showNotification('Ouverture du formulaire de ticket...', 'info');
}

function refreshDashboard() {
    SchlutterPortal.utils.showNotification('Tableau de bord actualisé', 'success');
}

function exportReport() {
    SchlutterPortal.utils.showNotification('Export du rapport en cours...', 'info');
}

function viewLogs() {
    window.location.href = 'logs.php';
}
</script>

<?php
// Pied de page
renderSchlutterFooter();
?>
