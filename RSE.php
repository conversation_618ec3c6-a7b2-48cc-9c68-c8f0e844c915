<?php
/**
 * Page RSE du portail intranet Schluter Systems
 */

require_once 'top_bar.php';

// Statistiques RSE
$rse_stats = [
    ['value' => '15', 'label' => 'Projets RSE'],
    ['value' => '85%', 'label' => 'Satisfaction Prestataires'],
    ['value' => '12', 'label' => 'Partenaires Communautaires'],
    ['value' => '95%', 'label' => 'Objectifs Atteints']
];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portail RSE - Schluter Systems</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <!-- Bannière RSE avec dégradé vert -->
    <section class="schluter-banner rse">
        <div class="banner-container">
            <h1 class="banner-title">Portail RSE</h1>
            <p class="banner-subtitle">Responsabilité Sociétale des Entreprises - Construisons un avenir durable ensemble</p>
        </div>
    </section>

    <main class="main-content">
        <!-- Section des statistiques RSE -->
        <section class="stats-section">
            <div class="stats-grid">
                <?php foreach ($rse_stats as $stat): ?>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $stat['value']; ?></div>
                        <div class="stat-label"><?php echo $stat['label']; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Section des services RSE -->
        <section class="services-section">
            <div class="services-grid">
                <!-- Carte 1: Évaluation des Prestataires -->
                <div class="service-card">
                    <div class="card-icon">
                        <i class="material-icons">assessment</i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Évaluation des Prestataires</h3>
                        <p class="card-description">Système d'évaluation et de suivi de la performance de nos partenaires et prestataires selon les critères RSE.</p>
                        <a href="evaluation.php" class="card-link">Accéder <i class="material-icons">arrow_forward</i></a>
                    </div>
                </div>

                <!-- Carte 2: Engagement Communautaire -->
                <div class="service-card">
                    <div class="card-icon">
                        <i class="material-icons">community</i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Engagement Communautaire</h3>
                        <p class="card-description">Gestion des projets communautaires, partenariats locaux et initiatives de développement durable.</p>
                        <a href="engagement_communautaire.php" class="card-link">Accéder <i class="material-icons">arrow_forward</i></a>
                    </div>
                </div>

                <!-- Carte 3: Rapports RSE -->
                <div class="service-card">
                    <div class="card-icon">
                        <i class="material-icons">report</i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Rapports RSE</h3>
                        <p class="card-description">Consultation et génération des rapports de responsabilité sociétale, indicateurs de performance et bilans.</p>
                        <a href="rapports_rse.php" class="card-link">Accéder <i class="material-icons">arrow_forward</i></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des initiatives en cours -->
        <section class="initiatives-section">
            <div class="initiatives-container">
                <h2 class="section-title">Initiatives RSE en Cours</h2>
                
                <div class="initiatives-grid">
                    <div class="initiative-card">
                        <div class="initiative-header">
                            <div class="initiative-status active">En cours</div>
                            <div class="initiative-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                <span class="progress-text">75%</span>
                            </div>
                        </div>
                        <h3 class="initiative-title">Réduction Empreinte Carbone</h3>
                        <p class="initiative-description">Programme de réduction de 30% des émissions CO2 d'ici fin 2025</p>
                        <div class="initiative-meta">
                            <span class="initiative-date">Début: Jan 2025</span>
                            <span class="initiative-team">Équipe: 8 personnes</span>
                        </div>
                    </div>

                    <div class="initiative-card">
                        <div class="initiative-header">
                            <div class="initiative-status planning">Planification</div>
                            <div class="initiative-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 25%"></div>
                                </div>
                                <span class="progress-text">25%</span>
                            </div>
                        </div>
                        <h3 class="initiative-title">Partenariat École Locale</h3>
                        <p class="initiative-description">Programme de formation et d'apprentissage avec les établissements locaux</p>
                        <div class="initiative-meta">
                            <span class="initiative-date">Début: Sep 2025</span>
                            <span class="initiative-team">Équipe: 5 personnes</span>
                        </div>
                    </div>

                    <div class="initiative-card">
                        <div class="initiative-header">
                            <div class="initiative-status completed">Terminé</div>
                            <div class="initiative-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%"></div>
                                </div>
                                <span class="progress-text">100%</span>
                            </div>
                        </div>
                        <h3 class="initiative-title">Audit Fournisseurs</h3>
                        <p class="initiative-description">Évaluation complète de nos 50 principaux fournisseurs selon les critères RSE</p>
                        <div class="initiative-meta">
                            <span class="initiative-date">Terminé: Mai 2025</span>
                            <span class="initiative-team">Équipe: 12 personnes</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des indicateurs environnementaux -->
        <section class="environmental-section">
            <div class="environmental-container">
                <h2 class="section-title">Indicateurs Environnementaux</h2>
                
                <div class="environmental-grid">
                    <div class="env-card">
                        <div class="env-icon">
                            <i class="material-icons">eco</i>
                        </div>
                        <div class="env-metric">
                            <span class="env-value">-18%</span>
                            <span class="env-label">Émissions CO2</span>
                        </div>
                        <div class="env-trend positive">
                            <i class="material-icons">trending_down</i>
                            <span>vs. 2024</span>
                        </div>
                    </div>

                    <div class="env-card">
                        <div class="env-icon">
                            <i class="material-icons">water_drop</i>
                        </div>
                        <div class="env-metric">
                            <span class="env-value">-12%</span>
                            <span class="env-label">Consommation Eau</span>
                        </div>
                        <div class="env-trend positive">
                            <i class="material-icons">trending_down</i>
                            <span>vs. 2024</span>
                        </div>
                    </div>

                    <div class="env-card">
                        <div class="env-icon">
                            <i class="material-icons">recycling</i>
                        </div>
                        <div class="env-metric">
                            <span class="env-value">+25%</span>
                            <span class="env-label">Taux Recyclage</span>
                        </div>
                        <div class="env-trend positive">
                            <i class="material-icons">trending_up</i>
                            <span>vs. 2024</span>
                        </div>
                    </div>

                    <div class="env-card">
                        <div class="env-icon">
                            <i class="material-icons">energy_savings_leaf</i>
                        </div>
                        <div class="env-metric">
                            <span class="env-value">-15%</span>
                            <span class="env-label">Consommation Énergie</span>
                        </div>
                        <div class="env-trend positive">
                            <i class="material-icons">trending_down</i>
                            <span>vs. 2024</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des actions rapides RSE -->
        <section class="rse-actions-section">
            <div class="rse-actions-container">
                <h2 class="section-title">Actions Rapides</h2>
                
                <div class="rse-actions-grid">
                    <button class="rse-action-btn" onclick="newEvaluation()">
                        <i class="material-icons">add_task</i>
                        <span>Nouvelle Évaluation</span>
                    </button>
                    
                    <button class="rse-action-btn" onclick="generateReport()">
                        <i class="material-icons">description</i>
                        <span>Générer Rapport</span>
                    </button>
                    
                    <button class="rse-action-btn" onclick="viewCalendar()">
                        <i class="material-icons">event</i>
                        <span>Calendrier RSE</span>
                    </button>
                    
                    <button class="rse-action-btn" onclick="contactTeam()">
                        <i class="material-icons">group</i>
                        <span>Équipe RSE</span>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <style>
    /* Styles spécifiques à la page RSE */
    :root {
        --rse-primary: #4CAF50;
        --rse-secondary: #2E7D32;
        --rse-accent: #81C784;
        --rse-light: #E8F5E8;
    }

    .schluter-banner.rse {
        background: linear-gradient(135deg, var(--rse-primary), var(--rse-secondary));
    }

    .main-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-title {
        text-align: center;
        font-size: 2.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 40px;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: var(--rse-primary);
    }

    /* Statistiques */
    .stats-section {
        margin: 50px 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--rse-primary);
        margin-bottom: 10px;
    }

    .stat-label {
        color: #666;
        font-weight: 500;
    }

    /* Services */
    .services-section {
        margin: 80px 0;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
    }

    .service-card {
        background: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        text-align: center;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .card-icon {
        background: var(--rse-primary);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
    }

    .card-icon .material-icons {
        font-size: 28px;
    }

    .card-title {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }

    .card-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .card-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: var(--rse-primary);
        color: white;
        text-decoration: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .card-link:hover {
        background: var(--rse-secondary);
    }

    /* Initiatives */
    .initiatives-section {
        margin: 80px 0;
        background: var(--rse-light);
        padding: 60px 0;
    }

    .initiatives-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .initiatives-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 30px;
    }

    .initiative-card {
        background: white;
        border-radius: 8px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .initiative-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .initiative-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .initiative-status.active {
        background: #E8F5E8;
        color: var(--rse-primary);
    }

    .initiative-status.planning {
        background: #FFF3E0;
        color: #FF9800;
    }

    .initiative-status.completed {
        background: #E3F2FD;
        color: #2196F3;
    }

    .initiative-progress {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .progress-bar {
        width: 80px;
        height: 6px;
        background: #E0E0E0;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: var(--rse-primary);
        transition: width 0.3s ease;
    }

    .progress-text {
        font-size: 0.8rem;
        font-weight: 500;
        color: #666;
    }

    .initiative-title {
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }

    .initiative-description {
        color: #666;
        line-height: 1.6;
        margin-bottom: 20px;
    }

    .initiative-meta {
        display: flex;
        justify-content: space-between;
        font-size: 0.9rem;
        color: #888;
    }

    /* Indicateurs environnementaux */
    .environmental-section {
        margin: 80px 0;
    }

    .environmental-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .environmental-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
    }

    .env-card {
        background: white;
        border-radius: 8px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .env-icon {
        background: var(--rse-light);
        color: var(--rse-primary);
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .env-metric {
        flex: 1;
    }

    .env-value {
        display: block;
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--rse-primary);
    }

    .env-label {
        color: #666;
        font-size: 0.9rem;
    }

    .env-trend {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.8rem;
    }

    .env-trend.positive {
        color: var(--rse-primary);
    }

    /* Actions RSE */
    .rse-actions-section {
        margin: 80px 0;
    }

    .rse-actions-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .rse-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .rse-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background: white;
        border: 2px solid var(--rse-primary);
        color: var(--rse-primary);
        padding: 25px 20px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
    }

    .rse-action-btn:hover {
        background: var(--rse-primary);
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    .rse-action-btn .material-icons {
        font-size: 2rem;
    }
    </style>

    <script>
    // Fonctions pour les actions RSE
    function newEvaluation() {
        alert('Ouverture du formulaire de nouvelle évaluation...');
    }

    function generateReport() {
        alert('Génération du rapport RSE en cours...');
    }

    function viewCalendar() {
        alert('Ouverture du calendrier RSE...');
    }

    function contactTeam() {
        alert('Contact de l\'équipe RSE...');
    }

    // Animation des barres de progression
    document.addEventListener('DOMContentLoaded', function() {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach(bar => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 500);
        });
    });
    </script>

<?php require_once 'bottom_bar.php'; ?>
</body>
</html>
