<?php
/**
 * Page Support Client - Schluter Systems
 */

require_once 'top_bar.php';

// Statistiques du support client
$support_stats = [
    ['value' => '24', 'label' => 'Tickets Ouverts'],
    ['value' => '4.8/5', 'label' => 'Satisfaction'],
    ['value' => '2.3h', 'label' => 'Temps Réponse'],
    ['value' => '94%', 'label' => 'Résolution J+1']
];

// Tickets récents
$recent_tickets = [
    [
        'id' => 'SUP-2025-045',
        'client' => 'Entreprise ABC',
        'subject' => 'Installation DITRA-HEAT',
        'priority' => 'Haute',
        'status' => 'En cours',
        'agent' => '<PERSON>',
        'created' => '2025-06-16 09:30'
    ],
    [
        'id' => 'SUP-2025-046',
        'client' => 'Construction XYZ',
        'subject' => 'Documentation technique',
        'priority' => 'Moyenne',
        'status' => 'En attente',
        'agent' => '<PERSON>',
        'created' => '2025-06-16 11:15'
    ],
    [
        'id' => 'SUP-2025-047',
        'client' => 'Rénovation Plus',
        'subject' => 'Problème de livraison',
        'priority' => 'Basse',
        'status' => 'Résolu',
        'agent' => 'Sophie Bernard',
        'created' => '2025-06-15 14:20'
    ]
];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Client - Schluter Systems</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <!-- Bannière Support Client -->
    <section class="schluter-banner adv">
        <div class="banner-container">
            <h1 class="banner-title">Support Client</h1>
            <p class="banner-subtitle">Excellence dans l'accompagnement et la satisfaction client</p>
        </div>
    </section>

    <main class="main-content">
        <!-- Section des statistiques -->
        <section class="stats-section">
            <div class="stats-grid">
                <?php foreach ($support_stats as $stat): ?>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $stat['value']; ?></div>
                        <div class="stat-label"><?php echo $stat['label']; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Section de création de ticket -->
        <section class="ticket-creation-section">
            <div class="ticket-creation-container">
                <h2 class="section-title">Nouveau Ticket de Support</h2>
                
                <div class="ticket-form-card">
                    <form id="ticketForm" class="ticket-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="clientName">Nom du Client</label>
                                <input type="text" id="clientName" name="clientName" required>
                            </div>
                            <div class="form-group">
                                <label for="clientEmail">Email</label>
                                <input type="email" id="clientEmail" name="clientEmail" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="priority">Priorité</label>
                                <select id="priority" name="priority" required>
                                    <option value="">Sélectionner une priorité</option>
                                    <option value="basse">Basse</option>
                                    <option value="moyenne">Moyenne</option>
                                    <option value="haute">Haute</option>
                                    <option value="critique">Critique</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="category">Catégorie</label>
                                <select id="category" name="category" required>
                                    <option value="">Sélectionner une catégorie</option>
                                    <option value="installation">Installation</option>
                                    <option value="technique">Support Technique</option>
                                    <option value="commande">Commande</option>
                                    <option value="livraison">Livraison</option>
                                    <option value="garantie">Garantie</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">Sujet</label>
                            <input type="text" id="subject" name="subject" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">Description</label>
                            <textarea id="description" name="description" rows="5" required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="attachments">Pièces jointes</label>
                            <input type="file" id="attachments" name="attachments" multiple>
                            <small>Formats acceptés: PDF, JPG, PNG, DOC (Max 10MB)</small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn-secondary" onclick="resetForm()">Annuler</button>
                            <button type="submit" class="btn-primary">Créer le Ticket</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Section des tickets récents -->
        <section class="recent-tickets-section">
            <div class="recent-tickets-container">
                <h2 class="section-title">Tickets Récents</h2>
                
                <div class="tickets-table">
                    <div class="table-header">
                        <div class="header-cell">ID Ticket</div>
                        <div class="header-cell">Client</div>
                        <div class="header-cell">Sujet</div>
                        <div class="header-cell">Priorité</div>
                        <div class="header-cell">Statut</div>
                        <div class="header-cell">Agent</div>
                        <div class="header-cell">Créé le</div>
                        <div class="header-cell">Actions</div>
                    </div>
                    
                    <?php foreach ($recent_tickets as $ticket): ?>
                    <div class="table-row">
                        <div class="table-cell">
                            <strong><?php echo $ticket['id']; ?></strong>
                        </div>
                        <div class="table-cell"><?php echo $ticket['client']; ?></div>
                        <div class="table-cell"><?php echo $ticket['subject']; ?></div>
                        <div class="table-cell">
                            <span class="priority-badge <?php echo strtolower($ticket['priority']); ?>">
                                <?php echo $ticket['priority']; ?>
                            </span>
                        </div>
                        <div class="table-cell">
                            <span class="status-badge <?php echo str_replace(' ', '-', strtolower($ticket['status'])); ?>">
                                <?php echo $ticket['status']; ?>
                            </span>
                        </div>
                        <div class="table-cell"><?php echo $ticket['agent']; ?></div>
                        <div class="table-cell">
                            <?php echo date('d/m/Y H:i', strtotime($ticket['created'])); ?>
                        </div>
                        <div class="table-cell">
                            <div class="action-buttons">
                                <button class="action-btn-small view" onclick="viewTicket('<?php echo $ticket['id']; ?>')">
                                    <i class="material-icons">visibility</i>
                                </button>
                                <button class="action-btn-small edit" onclick="editTicket('<?php echo $ticket['id']; ?>')">
                                    <i class="material-icons">edit</i>
                                </button>
                                <button class="action-btn-small message" onclick="messageClient('<?php echo $ticket['id']; ?>')">
                                    <i class="material-icons">message</i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- Section de la base de connaissances -->
        <section class="knowledge-base-section">
            <div class="knowledge-base-container">
                <h2 class="section-title">Base de Connaissances</h2>
                
                <div class="knowledge-grid">
                    <div class="knowledge-card">
                        <div class="knowledge-icon">
                            <i class="material-icons">build</i>
                        </div>
                        <h3>Guides d'Installation</h3>
                        <p>Instructions détaillées pour l'installation des produits Schluter</p>
                        <a href="#" class="knowledge-link">Consulter</a>
                    </div>
                    
                    <div class="knowledge-card">
                        <div class="knowledge-icon">
                            <i class="material-icons">help_outline</i>
                        </div>
                        <h3>FAQ Clients</h3>
                        <p>Réponses aux questions les plus fréquemment posées</p>
                        <a href="#" class="knowledge-link">Consulter</a>
                    </div>
                    
                    <div class="knowledge-card">
                        <div class="knowledge-icon">
                            <i class="material-icons">video_library</i>
                        </div>
                        <h3>Tutoriels Vidéo</h3>
                        <p>Bibliothèque de vidéos explicatives et démonstrations</p>
                        <a href="#" class="knowledge-link">Consulter</a>
                    </div>
                    
                    <div class="knowledge-card">
                        <div class="knowledge-icon">
                            <i class="material-icons">description</i>
                        </div>
                        <h3>Documentation Technique</h3>
                        <p>Fiches techniques et spécifications produits</p>
                        <a href="#" class="knowledge-link">Consulter</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des actions rapides -->
        <section class="quick-actions-section">
            <div class="quick-actions-container">
                <h2 class="section-title">Actions Rapides</h2>
                
                <div class="quick-actions-grid">
                    <button class="quick-action-btn" onclick="escalateTicket()">
                        <i class="material-icons">trending_up</i>
                        <span>Escalader Ticket</span>
                    </button>
                    
                    <button class="quick-action-btn" onclick="bulkUpdate()">
                        <i class="material-icons">update</i>
                        <span>Mise à Jour Groupée</span>
                    </button>
                    
                    <button class="quick-action-btn" onclick="generateReport()">
                        <i class="material-icons">assessment</i>
                        <span>Rapport Satisfaction</span>
                    </button>
                    
                    <button class="quick-action-btn" onclick="scheduleCallback()">
                        <i class="material-icons">schedule_send</i>
                        <span>Programmer Rappel</span>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <style>
    /* Styles spécifiques au support client */
    .ticket-creation-section {
        margin: 80px 0;
        background: var(--schluter-light-gray);
        padding: 60px 0;
    }

    .ticket-creation-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .ticket-form-card {
        background: var(--schluter-white);
        border-radius: var(--schluter-border-radius);
        padding: 40px;
        box-shadow: 0 4px 15px var(--schluter-shadow);
    }

    .ticket-form {
        max-width: 800px;
        margin: 0 auto;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--schluter-text);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--schluter-light-gray);
        border-radius: var(--schluter-border-radius);
        font-size: 1rem;
        transition: var(--schluter-transition);
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--schluter-primary);
    }

    .form-group small {
        display: block;
        margin-top: 5px;
        color: var(--schluter-dark-gray);
        font-size: 0.8rem;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 30px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 24px;
        border: none;
        border-radius: var(--schluter-border-radius);
        font-weight: 500;
        cursor: pointer;
        transition: var(--schluter-transition);
    }

    .btn-primary {
        background: var(--schluter-primary);
        color: var(--schluter-white);
    }

    .btn-primary:hover {
        background: var(--schluter-secondary);
    }

    .btn-secondary {
        background: var(--schluter-light-gray);
        color: var(--schluter-text);
    }

    .btn-secondary:hover {
        background: var(--schluter-dark-gray);
        color: var(--schluter-white);
    }

    /* Tableau des tickets */
    .recent-tickets-section {
        margin: 80px 0;
    }

    .recent-tickets-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .tickets-table {
        background: var(--schluter-white);
        border-radius: var(--schluter-border-radius);
        overflow: hidden;
        box-shadow: 0 4px 15px var(--schluter-shadow);
    }

    .table-header {
        display: grid;
        grid-template-columns: 120px 150px 200px 100px 120px 120px 140px 120px;
        background: var(--schluter-secondary);
        color: var(--schluter-white);
        font-weight: 600;
    }

    .header-cell {
        padding: 15px 10px;
        text-align: center;
        font-size: 0.9rem;
    }

    .table-row {
        display: grid;
        grid-template-columns: 120px 150px 200px 100px 120px 120px 140px 120px;
        border-bottom: 1px solid var(--schluter-light-gray);
        transition: var(--schluter-transition);
    }

    .table-row:hover {
        background: #f8f9fa;
    }

    .table-cell {
        padding: 15px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        font-size: 0.9rem;
    }

    .priority-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .priority-badge.haute {
        background: #FFEBEE;
        color: #C62828;
    }

    .priority-badge.moyenne {
        background: #FFF3E0;
        color: #EF6C00;
    }

    .priority-badge.basse {
        background: #E8F5E8;
        color: #2E7D32;
    }

    .priority-badge.critique {
        background: #FCE4EC;
        color: #AD1457;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .status-badge.en-cours {
        background: #E3F2FD;
        color: #1976D2;
    }

    .status-badge.en-attente {
        background: #FFF3E0;
        color: #EF6C00;
    }

    .status-badge.résolu {
        background: #E8F5E8;
        color: #2E7D32;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-btn-small {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: var(--schluter-transition);
    }

    .action-btn-small.view {
        color: var(--schluter-primary);
    }

    .action-btn-small.edit {
        color: #FF9800;
    }

    .action-btn-small.message {
        color: #4CAF50;
    }

    .action-btn-small:hover {
        background: rgba(0,0,0,0.1);
    }

    /* Base de connaissances */
    .knowledge-base-section {
        margin: 80px 0;
        background: var(--schluter-light-gray);
        padding: 60px 0;
    }

    .knowledge-base-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .knowledge-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
    }

    .knowledge-card {
        background: var(--schluter-white);
        border-radius: var(--schluter-border-radius);
        padding: 30px 20px;
        text-align: center;
        box-shadow: 0 4px 15px var(--schluter-shadow);
        transition: var(--schluter-transition);
    }

    .knowledge-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px var(--schluter-shadow);
    }

    .knowledge-icon {
        background: var(--schluter-secondary);
        color: var(--schluter-white);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
    }

    .knowledge-icon .material-icons {
        font-size: 28px;
    }

    .knowledge-card h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: var(--schluter-text);
    }

    .knowledge-card p {
        color: var(--schluter-dark-gray);
        margin-bottom: 20px;
        line-height: 1.5;
    }

    .knowledge-link {
        color: var(--schluter-primary);
        text-decoration: none;
        font-weight: 500;
        transition: var(--schluter-transition);
    }

    .knowledge-link:hover {
        color: var(--schluter-secondary);
    }

    /* Actions rapides */
    .quick-actions-section {
        margin: 80px 0;
    }

    .quick-actions-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .quick-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        background: var(--schluter-white);
        border: 2px solid var(--schluter-primary);
        color: var(--schluter-primary);
        padding: 25px 20px;
        border-radius: var(--schluter-border-radius);
        cursor: pointer;
        transition: var(--schluter-transition);
        font-weight: 500;
        box-shadow: 0 2px 10px var(--schluter-shadow);
    }

    .quick-action-btn:hover {
        background: var(--schluter-primary);
        color: var(--schluter-white);
        transform: translateY(-3px);
        box-shadow: 0 6px 20px var(--schluter-shadow);
    }

    .quick-action-btn .material-icons {
        font-size: 2rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .form-row {
            grid-template-columns: 1fr;
        }
        
        .table-header,
        .table-row {
            grid-template-columns: 1fr;
        }
        
        .header-cell,
        .table-cell {
            text-align: left;
            border-bottom: 1px solid var(--schluter-light-gray);
        }
        
        .form-actions {
            flex-direction: column;
        }
    }
    </style>

    <script>
    // Fonctions JavaScript pour le support client
    function resetForm() {
        document.getElementById('ticketForm').reset();
    }

    document.getElementById('ticketForm').addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Ticket créé avec succès !');
        resetForm();
    });

    function viewTicket(id) {
        alert('Affichage du ticket ' + id);
    }

    function editTicket(id) {
        alert('Édition du ticket ' + id);
    }

    function messageClient(id) {
        alert('Envoi de message pour le ticket ' + id);
    }

    function escalateTicket() {
        alert('Escalade de ticket...');
    }

    function bulkUpdate() {
        alert('Mise à jour groupée...');
    }

    function generateReport() {
        alert('Génération du rapport de satisfaction...');
    }

    function scheduleCallback() {
        alert('Programmation d\'un rappel...');
    }
    </script>

<?php require_once 'bottom_bar.php'; ?>
</body>
</html>
