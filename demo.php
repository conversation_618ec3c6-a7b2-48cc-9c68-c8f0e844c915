<?php
/**
 * Page de démonstration du portail Schluter Systems
 */

require_once 'templates/components.php';

// Génération de l'en-tête
renderSchlutterHeader('Démonstration du Portail');

// Navigation principale
renderSchlutterNavigation('accueil');

// Bannière
renderSchlutterBanner(
    'Démonstration du Portail',
    'Aperçu des fonctionnalités et composants du portail intranet Schluter Systems'
);
?>

<main class="main-content">
    <!-- Section de présentation -->
    <section class="demo-intro-section">
        <div class="demo-container">
            <h2 class="section-title">Portail Intranet Schluter Systems</h2>
            
            <div class="demo-description">
                <p>Ce portail intranet a été conçu pour centraliser l'accès aux ressources et services internes de Schluter Systems. Il comprend :</p>
                
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">computer</i>
                        </div>
                        <h3>Module Informatique</h3>
                        <ul>
                            <li>Gestion de l'inventaire</li>
                            <li>Accusés de réception</li>
                            <li>Changements de poste</li>
                            <li>Support technique</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">eco</i>
                        </div>
                        <h3>Module RSE</h3>
                        <ul>
                            <li>Évaluation des prestataires</li>
                            <li>Engagement communautaire</li>
                            <li>Rapports RSE</li>
                            <li>Indicateurs environnementaux</li>
                        </ul>
                    </div>
                    
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="material-icons">support</i>
                        </div>
                        <h3>Module ADV</h3>
                        <ul>
                            <li>Support client</li>
                            <li>Gestion des retours</li>
                            <li>Tickets de support</li>
                            <li>Base de connaissances</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des liens de navigation -->
    <section class="demo-navigation-section">
        <div class="demo-container">
            <h2 class="section-title">Navigation du Portail</h2>
            
            <div class="navigation-demo">
                <div class="nav-demo-card">
                    <h3>Pages Principales</h3>
                    <div class="nav-links">
                        <a href="index.php" class="nav-demo-link">
                            <i class="material-icons">home</i>
                            <span>Page d'Accueil</span>
                        </a>
                        <a href="IT.php" class="nav-demo-link">
                            <i class="material-icons">computer</i>
                            <span>Portail Informatique</span>
                        </a>
                        <a href="RSE.php" class="nav-demo-link">
                            <i class="material-icons">eco</i>
                            <span>Portail RSE</span>
                        </a>
                        <a href="ADV.php" class="nav-demo-link">
                            <i class="material-icons">support</i>
                            <span>Portail ADV</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-demo-card">
                    <h3>Sous-Pages IT</h3>
                    <div class="nav-links">
                        <a href="inventaire.php" class="nav-demo-link">
                            <i class="material-icons">inventory</i>
                            <span>Inventaire</span>
                        </a>
                        <a href="accuse_reception.php" class="nav-demo-link">
                            <i class="material-icons">receipt</i>
                            <span>Accusés de Réception</span>
                        </a>
                        <a href="End_Of_Life.php" class="nav-demo-link">
                            <i class="material-icons">refresh</i>
                            <span>Changements de Poste</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-demo-card">
                    <h3>Sous-Pages RSE</h3>
                    <div class="nav-links">
                        <a href="evaluation.php" class="nav-demo-link">
                            <i class="material-icons">assessment</i>
                            <span>Évaluation Prestataires</span>
                        </a>
                        <a href="engagement_communautaire.php" class="nav-demo-link">
                            <i class="material-icons">community</i>
                            <span>Engagement Communautaire</span>
                        </a>
                        <a href="rapports_rse.php" class="nav-demo-link">
                            <i class="material-icons">report</i>
                            <span>Rapports RSE</span>
                        </a>
                    </div>
                </div>
                
                <div class="nav-demo-card">
                    <h3>Sous-Pages ADV</h3>
                    <div class="nav-links">
                        <a href="support_client.php" class="nav-demo-link">
                            <i class="material-icons">help</i>
                            <span>Support Client</span>
                        </a>
                        <a href="gestion_retours.php" class="nav-demo-link">
                            <i class="material-icons">assignment_return</i>
                            <span>Gestion des Retours</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des composants techniques -->
    <section class="demo-technical-section">
        <div class="demo-container">
            <h2 class="section-title">Architecture Technique</h2>
            
            <div class="technical-info">
                <div class="tech-card">
                    <h3>Structure des Fichiers</h3>
                    <div class="file-structure">
                        <div class="folder">
                            <i class="material-icons">folder</i>
                            <span>config/</span>
                            <div class="file-list">
                                <div class="file">schluter-config.php</div>
                            </div>
                        </div>
                        <div class="folder">
                            <i class="material-icons">folder</i>
                            <span>templates/</span>
                            <div class="file-list">
                                <div class="file">components.php</div>
                            </div>
                        </div>
                        <div class="folder">
                            <i class="material-icons">folder</i>
                            <span>css/</span>
                            <div class="file-list">
                                <div class="file">schluter-unified.css</div>
                                <div class="file">styles.css</div>
                            </div>
                        </div>
                        <div class="folder">
                            <i class="material-icons">folder</i>
                            <span>js/</span>
                            <div class="file-list">
                                <div class="file">schluter-common.js</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="tech-card">
                    <h3>Composants Réutilisables</h3>
                    <ul class="component-list">
                        <li><code>renderSchlutterHeader()</code> - En-tête HTML</li>
                        <li><code>renderSchlutterNavigation()</code> - Navigation principale</li>
                        <li><code>renderSchlutterSubNavigation()</code> - Sous-navigation</li>
                        <li><code>renderSchlutterBanner()</code> - Bannières</li>
                        <li><code>renderServiceCard()</code> - Cartes de service</li>
                        <li><code>renderStatsGrid()</code> - Grilles de statistiques</li>
                        <li><code>renderSchlutterFooter()</code> - Pied de page</li>
                    </ul>
                </div>
                
                <div class="tech-card">
                    <h3>Fonctionnalités JavaScript</h3>
                    <ul class="feature-list">
                        <li>Navigation responsive</li>
                        <li>Animations des cartes</li>
                        <li>Système de notifications</li>
                        <li>Tooltips interactifs</li>
                        <li>Compteurs animés</li>
                        <li>Menu mobile</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Section des couleurs et thème -->
    <section class="demo-theme-section">
        <div class="demo-container">
            <h2 class="section-title">Palette de Couleurs Schluter</h2>
            
            <div class="color-palette">
                <div class="color-card">
                    <div class="color-sample" style="background: #E85A2B;"></div>
                    <div class="color-info">
                        <strong>Primary</strong>
                        <span>#E85A2B</span>
                        <small>Orange Schluter</small>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-sample" style="background: #2B4C85;"></div>
                    <div class="color-info">
                        <strong>Secondary</strong>
                        <span>#2B4C85</span>
                        <small>Bleu foncé</small>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-sample" style="background: #F4A261;"></div>
                    <div class="color-info">
                        <strong>Accent</strong>
                        <span>#F4A261</span>
                        <small>Orange clair</small>
                    </div>
                </div>
                
                <div class="color-card">
                    <div class="color-sample" style="background: #F8F9FA;"></div>
                    <div class="color-info">
                        <strong>Background</strong>
                        <span>#F8F9FA</span>
                        <small>Gris très clair</small>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<style>
/* Styles spécifiques à la page de démonstration */
.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.demo-intro-section {
    margin: 50px 0;
}

.demo-description {
    text-align: center;
    margin-bottom: 50px;
}

.demo-description p {
    font-size: 1.1rem;
    color: var(--schluter-dark-gray);
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.feature-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 30px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
    text-align: center;
}

.feature-icon {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.feature-icon .material-icons {
    font-size: 28px;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--schluter-text);
}

.feature-card ul {
    list-style: none;
    padding: 0;
    text-align: left;
}

.feature-card li {
    padding: 8px 0;
    border-bottom: 1px solid var(--schluter-light-gray);
    color: var(--schluter-dark-gray);
}

.feature-card li:last-child {
    border-bottom: none;
}

/* Navigation demo */
.demo-navigation-section {
    margin: 80px 0;
    background: var(--schluter-light-gray);
    padding: 60px 0;
}

.navigation-demo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.nav-demo-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 25px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.nav-demo-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--schluter-text);
    text-align: center;
}

.nav-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.nav-demo-link {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    text-decoration: none;
    color: var(--schluter-text);
    border-radius: var(--schluter-border-radius);
    transition: var(--schluter-transition);
}

.nav-demo-link:hover {
    background: var(--schluter-primary);
    color: var(--schluter-white);
}

.nav-demo-link .material-icons {
    font-size: 20px;
}

/* Section technique */
.demo-technical-section {
    margin: 80px 0;
}

.technical-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.tech-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 30px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.tech-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--schluter-text);
}

.file-structure {
    font-family: 'Courier New', monospace;
}

.folder {
    margin-bottom: 15px;
}

.folder > span {
    font-weight: 600;
    color: var(--schluter-primary);
}

.file-list {
    margin-left: 25px;
    margin-top: 8px;
}

.file {
    padding: 4px 0;
    color: var(--schluter-dark-gray);
    font-size: 0.9rem;
}

.component-list,
.feature-list {
    list-style: none;
    padding: 0;
}

.component-list li,
.feature-list li {
    padding: 8px 0;
    border-bottom: 1px solid var(--schluter-light-gray);
    color: var(--schluter-dark-gray);
}

.component-list li:last-child,
.feature-list li:last-child {
    border-bottom: none;
}

.component-list code {
    background: var(--schluter-light-gray);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: var(--schluter-primary);
}

/* Palette de couleurs */
.demo-theme-section {
    margin: 80px 0;
    background: var(--schluter-light-gray);
    padding: 60px 0;
}

.color-palette {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
}

.color-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px var(--schluter-shadow);
}

.color-sample {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.color-info strong {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--schluter-text);
}

.color-info span {
    display: block;
    font-family: 'Courier New', monospace;
    color: var(--schluter-dark-gray);
    margin-bottom: 5px;
}

.color-info small {
    color: var(--schluter-dark-gray);
    font-style: italic;
}

/* Responsive */
@media (max-width: 768px) {
    .features-grid,
    .navigation-demo,
    .technical-info,
    .color-palette {
        grid-template-columns: 1fr;
    }
}
</style>

<?php
// Pied de page
renderSchlutterFooter();
?>
