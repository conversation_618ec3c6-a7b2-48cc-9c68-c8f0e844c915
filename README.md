# Portail Intranet Schluter Systems

Un portail intranet moderne et complet pour la gestion des ressources internes de Schluter Systems.

## 🚀 Fonctionnalités

### Pages Principales
- **Page d'Accueil** (`index.php`) - Hub central avec statistiques et liens rapides
- **Portail Informatique** (`IT.php`) - Gestion des ressources IT avec graphiques
- **Portail RSE** (`RSE.php`) - Responsabilité sociétale des entreprises
- **Portail ADV** (`ADV.php`) - Service après-vente et support client

### Modules Informatique
- **Inventaire** (`inventaire.php`) - Gestion complète des équipements
- **Accusés de Réception** (`accuse_reception.php`) - Suivi des livraisons
- **Changements de Poste** (`End_Of_Life.php`) - Gestion des fins de vie

### Modules RSE
- **Évaluation des Prestataires** (`evaluation.php`) - Système d'évaluation
- **Engagement Communautaire** (`engagement_communautaire.php`) - Projets communautaires
- **Rapports RSE** (`rapports_rse.php`) - Reporting de durabilité

### Modules ADV
- **Support Client** (`support_client.php`) - Gestion des tickets de support
- **Gestion des Retours** (`gestion_retours.php`) - Traitement des retours produits

## 🏗️ Architecture

### Structure des Dossiers
```
/
├── config/
│   └── schluter-config.php      # Configuration principale
├── templates/
│   └── components.php           # Composants réutilisables
├── css/
│   ├── schluter-unified.css     # Styles unifiés
│   └── styles.css               # Styles pour RSE/ADV
├── js/
│   └── schluter-common.js       # JavaScript commun
├── images/
│   └── placeholder.txt          # Dossier pour les images
├── *.php                        # Pages du portail
└── README.md                    # Documentation
```

### Composants Réutilisables
- `renderSchlutterHeader()` - En-tête HTML standardisé
- `renderSchlutterNavigation()` - Navigation principale
- `renderSchlutterSubNavigation()` - Sous-navigation modulaire
- `renderSchlutterBanner()` - Bannières avec dégradés
- `renderServiceCard()` - Cartes de service uniformes
- `renderStatsGrid()` - Grilles de statistiques
- `renderSchlutterFooter()` - Pied de page complet

## 🎨 Design System

### Palette de Couleurs Schluter
- **Primary**: `#E85A2B` (Orange Schluter)
- **Secondary**: `#2B4C85` (Bleu foncé)
- **Accent**: `#F4A261` (Orange clair)
- **Background**: `#F8F9FA` (Gris très clair)
- **Text**: `#333333` (Gris foncé)

### Typographie
- **Police principale**: Inter (Google Fonts)
- **Icônes**: Material Icons
- **Tailles**: Système de tailles cohérent

### Composants UI
- Cartes avec ombres et animations
- Boutons avec états de survol
- Navigation responsive
- Grilles adaptatives
- Formulaires stylisés

## 🛠️ Technologies

### Frontend
- **HTML5** - Structure sémantique
- **CSS3** - Variables CSS, Grid, Flexbox
- **JavaScript ES6+** - Interactions et animations
- **Material Icons** - Iconographie cohérente

### Backend
- **PHP 8+** - Logique serveur
- **Architecture modulaire** - Composants réutilisables
- **Configuration centralisée** - Gestion unifiée

### Intégrations
- **Chart.js** - Graphiques interactifs (page IT)
- **Google Fonts** - Typographie web
- **Responsive Design** - Compatible mobile/tablette

## 📱 Fonctionnalités JavaScript

### SchlutterPortal Object
- **Navigation**: Gestion des liens actifs et animations
- **Service Cards**: Animations au survol et clic
- **Mobile Menu**: Menu responsive automatique
- **Tooltips**: Système de tooltips interactifs
- **Notifications**: Système de notifications toast
- **Utilities**: Fonctions utilitaires (compteurs, dates, etc.)

### Animations
- Transitions fluides sur tous les éléments
- Animations de cartes au survol
- Barres de progression animées
- Effets de chargement

## 🚀 Installation

### Prérequis
- Serveur web (Apache/Nginx)
- PHP 8.0 ou supérieur
- Navigateur moderne

### Étapes d'Installation
1. **Cloner/Télécharger** les fichiers dans votre répertoire web
2. **Configurer** le serveur web pour pointer vers le dossier
3. **Ajouter les logos** dans le dossier `images/`:
   - `schluter-logo.png` (200x60px)
   - `schluter-logo-white.png` (150x45px)
4. **Tester** en accédant à `index.php`

### Configuration
Modifier `config/schluter-config.php` pour :
- Ajuster les paramètres de base de données
- Personnaliser la navigation
- Modifier les couleurs si nécessaire

## 📄 Pages Disponibles

### Pages Principales
- `index.php` - Page d'accueil
- `IT.php` - Portail informatique
- `RSE.php` - Portail RSE
- `ADV.php` - Portail ADV
- `demo.php` - Page de démonstration

### Sous-Pages Fonctionnelles
- `inventaire.php` - Inventaire informatique complet
- `support_client.php` - Support client avec formulaires

### Composants Communs
- `top_bar.php` - Barre de navigation pour RSE/ADV
- `bottom_bar.php` - Pied de page pour RSE/ADV

## 🎯 Utilisation

### Navigation
- **Menu principal** : Accès aux modules principaux
- **Sous-menus** : Navigation contextuelle par module
- **Liens rapides** : Accès direct aux fonctions courantes

### Fonctionnalités Interactives
- **Recherche et filtres** : Dans les pages de gestion
- **Formulaires** : Création de tickets, évaluations
- **Tableaux** : Tri et pagination des données
- **Actions rapides** : Boutons d'action contextuels

## 🔧 Personnalisation

### Couleurs
Modifier les variables CSS dans `css/schluter-unified.css` :
```css
:root {
    --schluter-primary: #E85A2B;
    --schluter-secondary: #2B4C85;
    /* ... autres couleurs */
}
```

### Navigation
Ajuster `$navigation_config` dans `config/schluter-config.php`

### Composants
Créer de nouveaux composants dans `templates/components.php`

## 📊 Fonctionnalités Avancées

### Graphiques (Page IT)
- Utilisation de Chart.js
- Graphiques en temps réel
- Métriques de performance

### Responsive Design
- Adaptation automatique mobile/tablette
- Menu hamburger sur mobile
- Grilles adaptatives

### Accessibilité
- Navigation au clavier
- Contrastes respectés
- Sémantique HTML appropriée

## 🤝 Support

Pour toute question ou assistance :
- Consulter la page `demo.php` pour un aperçu complet
- Vérifier la configuration dans `config/schluter-config.php`
- Tester les composants individuellement

## 📝 Notes de Développement

### Standards de Code
- PHP PSR-12 compatible
- CSS BEM methodology
- JavaScript ES6+ features
- Commentaires complets

### Performance
- CSS optimisé et minifiable
- JavaScript modulaire
- Images optimisées recommandées
- Chargement progressif

---

**Développé pour Schluter Systems** - Portail intranet moderne et évolutif
