<?php
/**
 * Composants réutilisables pour le portail Schluter Systems
 */

require_once 'config/schluter-config.php';

/**
 * Génère l'en-tête HTML standardisé
 */
function renderSchlutterHeader($page_title = '', $additional_css = [], $additional_js = []) {
    global $schluter_colors;
    
    $full_title = $page_title ? $page_title . ' - ' . SITE_TITLE : SITE_TITLE;
    
    echo "<!DOCTYPE html>\n";
    echo "<html lang='fr'>\n";
    echo "<head>\n";
    echo "    <meta charset='UTF-8'>\n";
    echo "    <meta name='viewport' content='width=device-width, initial-scale=1.0'>\n";
    echo "    <title>{$full_title}</title>\n";
    echo "    <link rel='stylesheet' href='css/schluter-unified.css'>\n";
    
    // CSS additionnels
    foreach ($additional_css as $css) {
        echo "    <link rel='stylesheet' href='{$css}'>\n";
    }
    
    // Google Fonts
    echo "    <link href='https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' rel='stylesheet'>\n";
    echo "    <link href='https://fonts.googleapis.com/icon?family=Material+Icons' rel='stylesheet'>\n";
    
    echo "</head>\n";
    echo "<body>\n";
    
    // JavaScript additionnels
    foreach ($additional_js as $js) {
        echo "    <script src='{$js}'></script>\n";
    }
}

/**
 * Affiche la navigation principale
 */
function renderSchlutterNavigation($current_module = '') {
    global $navigation_config;
    
    echo "<nav class='schluter-nav'>\n";
    echo "    <div class='nav-container'>\n";
    echo "        <div class='nav-brand'>\n";
    echo "            <img src='images/schluter-logo.png' alt='Schluter Systems' class='logo'>\n";
    echo "            <span class='brand-text'>" . COMPANY_NAME . "</span>\n";
    echo "        </div>\n";
    echo "        <ul class='nav-menu'>\n";
    
    foreach ($navigation_config as $key => $item) {
        $active_class = ($current_module === $key) ? ' active' : '';
        echo "            <li class='nav-item{$active_class}'>\n";
        echo "                <a href='{$item['url']}' class='nav-link'>\n";
        echo "                    <i class='material-icons'>{$item['icon']}</i>\n";
        echo "                    <span>{$item['title']}</span>\n";
        echo "                </a>\n";
        echo "            </li>\n";
    }
    
    echo "        </ul>\n";
    echo "        <div class='nav-user'>\n";
    echo "            <span class='user-name'>Utilisateur</span>\n";
    echo "            <i class='material-icons'>account_circle</i>\n";
    echo "        </div>\n";
    echo "    </div>\n";
    echo "</nav>\n";
}

/**
 * Affiche la sous-navigation pour un module
 */
function renderSchlutterSubNavigation($module_key) {
    global $navigation_config;
    
    if (!isset($navigation_config[$module_key]['sub_modules'])) {
        return;
    }
    
    $sub_modules = $navigation_config[$module_key]['sub_modules'];
    
    echo "<nav class='schluter-subnav'>\n";
    echo "    <div class='subnav-container'>\n";
    echo "        <ul class='subnav-menu'>\n";
    
    foreach ($sub_modules as $key => $item) {
        echo "            <li class='subnav-item'>\n";
        echo "                <a href='{$item['url']}' class='subnav-link'>\n";
        echo "                    <i class='material-icons'>{$item['icon']}</i>\n";
        echo "                    <span>{$item['title']}</span>\n";
        echo "                </a>\n";
        echo "            </li>\n";
    }
    
    echo "        </ul>\n";
    echo "    </div>\n";
    echo "</nav>\n";
}

/**
 * Affiche une bannière avec titre et sous-titre
 */
function renderSchlutterBanner($title, $subtitle = '', $background_class = 'default') {
    echo "<section class='schluter-banner {$background_class}'>\n";
    echo "    <div class='banner-container'>\n";
    echo "        <h1 class='banner-title'>{$title}</h1>\n";
    if ($subtitle) {
        echo "        <p class='banner-subtitle'>{$subtitle}</p>\n";
    }
    echo "    </div>\n";
    echo "</section>\n";
}

/**
 * Affiche une carte de service
 */
function renderServiceCard($title, $description, $link, $icon = 'business') {
    echo "<div class='service-card'>\n";
    echo "    <div class='card-icon'>\n";
    echo "        <i class='material-icons'>{$icon}</i>\n";
    echo "    </div>\n";
    echo "    <div class='card-content'>\n";
    echo "        <h3 class='card-title'>{$title}</h3>\n";
    echo "        <p class='card-description'>{$description}</p>\n";
    echo "        <a href='{$link}' class='card-link'>Accéder <i class='material-icons'>arrow_forward</i></a>\n";
    echo "    </div>\n";
    echo "</div>\n";
}

/**
 * Affiche le pied de page
 */
function renderSchlutterFooter() {
    echo "<footer class='schluter-footer'>\n";
    echo "    <div class='footer-container'>\n";
    echo "        <div class='footer-content'>\n";
    echo "            <div class='footer-section'>\n";
    echo "                <h4>Schluter Systems</h4>\n";
    echo "                <p>Portail intranet pour la gestion des ressources internes</p>\n";
    echo "            </div>\n";
    echo "            <div class='footer-section'>\n";
    echo "                <h4>Support</h4>\n";
    echo "                <p>IT Helpdesk: ext. 1234</p>\n";
    echo "                <p>Email: <EMAIL></p>\n";
    echo "            </div>\n";
    echo "            <div class='footer-section'>\n";
    echo "                <h4>Liens Rapides</h4>\n";
    echo "                <a href='assistance.php'>Assistance</a>\n";
    echo "                <a href='dashboard.php'>Tableau de Bord</a>\n";
    echo "            </div>\n";
    echo "        </div>\n";
    echo "        <div class='footer-bottom'>\n";
    echo "            <p>&copy; " . date('Y') . " Schluter Systems. Tous droits réservés.</p>\n";
    echo "        </div>\n";
    echo "    </div>\n";
    echo "</footer>\n";
    echo "<script src='js/schluter-common.js'></script>\n";
    echo "</body>\n";
    echo "</html>\n";
}

/**
 * Affiche une grille de statistiques
 */
function renderStatsGrid($stats) {
    echo "<div class='stats-grid'>\n";
    foreach ($stats as $stat) {
        echo "    <div class='stat-card'>\n";
        echo "        <div class='stat-value'>{$stat['value']}</div>\n";
        echo "        <div class='stat-label'>{$stat['label']}</div>\n";
        echo "    </div>\n";
    }
    echo "</div>\n";
}
?>
