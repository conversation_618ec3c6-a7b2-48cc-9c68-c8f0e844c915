<?php
/**
 * Page ADV du portail intranet Schluter Systems
 */

require_once 'top_bar.php';

// Statistiques ADV
$adv_stats = [
    ['value' => '245', 'label' => 'Tickets Ouverts'],
    ['value' => '92%', 'label' => 'Satisfaction Client'],
    ['value' => '48h', 'label' => 'Temps Moyen Résolution'],
    ['value' => '156', 'label' => 'Retours Traités']
];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portail ADV - Schluter Systems</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <!-- Bannière ADV -->
    <section class="schluter-banner adv">
        <div class="banner-container">
            <h1 class="banner-title">Portail ADV</h1>
            <p class="banner-subtitle">Service Après-Vente - Excellence dans l'accompagnement client</p>
        </div>
    </section>

    <main class="main-content">
        <!-- Section des statistiques ADV -->
        <section class="stats-section">
            <div class="stats-grid">
                <?php foreach ($adv_stats as $stat): ?>
                    <div class="stat-card">
                        <div class="stat-value"><?php echo $stat['value']; ?></div>
                        <div class="stat-label"><?php echo $stat['label']; ?></div>
                    </div>
                <?php endforeach; ?>
            </div>
        </section>

        <!-- Section des services ADV -->
        <section class="services-section">
            <div class="services-grid">
                <!-- Carte 1: Support Client -->
                <div class="service-card">
                    <div class="card-icon">
                        <i class="material-icons">help</i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Support Client</h3>
                        <p class="card-description">Gestion des demandes de support, tickets d'assistance et suivi de la satisfaction client.</p>
                        <a href="support_client.php" class="card-link">Accéder <i class="material-icons">arrow_forward</i></a>
                    </div>
                </div>

                <!-- Carte 2: Gestion des Retours -->
                <div class="service-card">
                    <div class="card-icon">
                        <i class="material-icons">assignment_return</i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Gestion des Retours</h3>
                        <p class="card-description">Traitement des retours produits, remboursements et échanges selon les politiques de garantie.</p>
                        <a href="gestion_retours.php" class="card-link">Accéder <i class="material-icons">arrow_forward</i></a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des tickets récents -->
        <section class="tickets-section">
            <div class="tickets-container">
                <h2 class="section-title">Tickets Récents</h2>
                
                <div class="tickets-table">
                    <div class="table-header">
                        <div class="header-cell">ID</div>
                        <div class="header-cell">Client</div>
                        <div class="header-cell">Sujet</div>
                        <div class="header-cell">Priorité</div>
                        <div class="header-cell">Statut</div>
                        <div class="header-cell">Assigné à</div>
                        <div class="header-cell">Actions</div>
                    </div>
                    
                    <div class="table-row">
                        <div class="table-cell">#2025-001</div>
                        <div class="table-cell">Entreprise ABC</div>
                        <div class="table-cell">Problème installation DITRA-HEAT</div>
                        <div class="table-cell">
                            <span class="priority high">Haute</span>
                        </div>
                        <div class="table-cell">
                            <span class="status in-progress">En cours</span>
                        </div>
                        <div class="table-cell">Marie Dupont</div>
                        <div class="table-cell">
                            <button class="action-btn view" onclick="viewTicket('2025-001')">
                                <i class="material-icons">visibility</i>
                            </button>
                            <button class="action-btn edit" onclick="editTicket('2025-001')">
                                <i class="material-icons">edit</i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-row">
                        <div class="table-cell">#2025-002</div>
                        <div class="table-cell">Construction XYZ</div>
                        <div class="table-cell">Demande de documentation technique</div>
                        <div class="table-cell">
                            <span class="priority medium">Moyenne</span>
                        </div>
                        <div class="table-cell">
                            <span class="status pending">En attente</span>
                        </div>
                        <div class="table-cell">Jean Martin</div>
                        <div class="table-cell">
                            <button class="action-btn view" onclick="viewTicket('2025-002')">
                                <i class="material-icons">visibility</i>
                            </button>
                            <button class="action-btn edit" onclick="editTicket('2025-002')">
                                <i class="material-icons">edit</i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-row">
                        <div class="table-cell">#2025-003</div>
                        <div class="table-cell">Rénovation Plus</div>
                        <div class="table-cell">Retour produit défectueux</div>
                        <div class="table-cell">
                            <span class="priority low">Basse</span>
                        </div>
                        <div class="table-cell">
                            <span class="status resolved">Résolu</span>
                        </div>
                        <div class="table-cell">Sophie Bernard</div>
                        <div class="table-cell">
                            <button class="action-btn view" onclick="viewTicket('2025-003')">
                                <i class="material-icons">visibility</i>
                            </button>
                            <button class="action-btn edit" onclick="editTicket('2025-003')">
                                <i class="material-icons">edit</i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="table-footer">
                    <button class="load-more-btn" onclick="loadMoreTickets()">
                        Charger plus de tickets
                    </button>
                </div>
            </div>
        </section>

        <!-- Section des métriques de performance -->
        <section class="performance-section">
            <div class="performance-container">
                <h2 class="section-title">Métriques de Performance</h2>
                
                <div class="performance-grid">
                    <div class="metric-card">
                        <div class="metric-header">
                            <h3>Temps de Réponse</h3>
                            <i class="material-icons">schedule</i>
                        </div>
                        <div class="metric-value">2.5h</div>
                        <div class="metric-trend positive">
                            <i class="material-icons">trending_down</i>
                            <span>-15% vs mois dernier</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <h3>Taux de Résolution</h3>
                            <i class="material-icons">check_circle</i>
                        </div>
                        <div class="metric-value">94%</div>
                        <div class="metric-trend positive">
                            <i class="material-icons">trending_up</i>
                            <span>+3% vs mois dernier</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <h3>Satisfaction Client</h3>
                            <i class="material-icons">sentiment_satisfied</i>
                        </div>
                        <div class="metric-value">4.6/5</div>
                        <div class="metric-trend positive">
                            <i class="material-icons">trending_up</i>
                            <span>+0.2 vs mois dernier</span>
                        </div>
                    </div>
                    
                    <div class="metric-card">
                        <div class="metric-header">
                            <h3>Tickets Fermés</h3>
                            <i class="material-icons">done_all</i>
                        </div>
                        <div class="metric-value">187</div>
                        <div class="metric-trend positive">
                            <i class="material-icons">trending_up</i>
                            <span>+12% vs mois dernier</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section des actions rapides ADV -->
        <section class="adv-actions-section">
            <div class="adv-actions-container">
                <h2 class="section-title">Actions Rapides</h2>
                
                <div class="adv-actions-grid">
                    <button class="adv-action-btn primary" onclick="createTicket()">
                        <i class="material-icons">add</i>
                        <span>Nouveau Ticket</span>
                    </button>
                    
                    <button class="adv-action-btn secondary" onclick="processReturn()">
                        <i class="material-icons">assignment_return</i>
                        <span>Traiter Retour</span>
                    </button>
                    
                    <button class="adv-action-btn accent" onclick="generateReport()">
                        <i class="material-icons">analytics</i>
                        <span>Rapport Mensuel</span>
                    </button>
                    
                    <button class="adv-action-btn info" onclick="viewKnowledgeBase()">
                        <i class="material-icons">library_books</i>
                        <span>Base de Connaissances</span>
                    </button>
                </div>
            </div>
        </section>
    </main>

    <style>
    /* Styles spécifiques à la page ADV */
    :root {
        --adv-primary: #2B4C85;
        --adv-secondary: #1565C0;
        --adv-accent: #64B5F6;
        --adv-light: #E3F2FD;
    }

    .schluter-banner.adv {
        background: linear-gradient(135deg, var(--adv-primary), var(--adv-secondary));
    }

    .main-content {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .section-title {
        text-align: center;
        font-size: 2.2rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 40px;
        position: relative;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: var(--adv-primary);
    }

    /* Statistiques */
    .stats-section {
        margin: 50px 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--adv-primary);
        margin-bottom: 10px;
    }

    .stat-label {
        color: #666;
        font-weight: 500;
    }

    /* Services */
    .services-section {
        margin: 80px 0;
    }

    .services-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 30px;
    }

    .service-card {
        background: white;
        border-radius: 8px;
        padding: 30px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        text-align: center;
    }

    .service-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .card-icon {
        background: var(--adv-primary);
        color: white;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
    }

    .card-icon .material-icons {
        font-size: 28px;
    }

    .card-title {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }

    .card-description {
        color: #666;
        margin-bottom: 20px;
        line-height: 1.6;
    }

    .card-link {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: var(--adv-primary);
        color: white;
        text-decoration: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .card-link:hover {
        background: var(--adv-secondary);
    }

    /* Table des tickets */
    .tickets-section {
        margin: 80px 0;
        background: var(--adv-light);
        padding: 60px 0;
    }

    .tickets-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .tickets-table {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .table-header {
        display: grid;
        grid-template-columns: 100px 150px 2fr 100px 120px 150px 100px;
        background: var(--adv-primary);
        color: white;
        font-weight: 600;
    }

    .header-cell {
        padding: 15px 10px;
        text-align: center;
    }

    .table-row {
        display: grid;
        grid-template-columns: 100px 150px 2fr 100px 120px 150px 100px;
        border-bottom: 1px solid #eee;
    }

    .table-row:hover {
        background: #f8f9fa;
    }

    .table-cell {
        padding: 15px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .priority {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .priority.high {
        background: #FFEBEE;
        color: #C62828;
    }

    .priority.medium {
        background: #FFF3E0;
        color: #EF6C00;
    }

    .priority.low {
        background: #E8F5E8;
        color: #2E7D32;
    }

    .status {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .status.in-progress {
        background: #E3F2FD;
        color: var(--adv-primary);
    }

    .status.pending {
        background: #FFF3E0;
        color: #EF6C00;
    }

    .status.resolved {
        background: #E8F5E8;
        color: #2E7D32;
    }

    .action-btn {
        background: none;
        border: none;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        margin: 0 2px;
        transition: all 0.3s ease;
    }

    .action-btn.view {
        color: var(--adv-primary);
    }

    .action-btn.edit {
        color: #FF9800;
    }

    .action-btn:hover {
        background: rgba(0,0,0,0.1);
    }

    .table-footer {
        padding: 20px;
        text-align: center;
        background: white;
    }

    .load-more-btn {
        background: var(--adv-primary);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .load-more-btn:hover {
        background: var(--adv-secondary);
    }

    /* Métriques de performance */
    .performance-section {
        margin: 80px 0;
    }

    .performance-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .performance-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 25px;
    }

    .metric-card {
        background: white;
        border-radius: 8px;
        padding: 25px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .metric-header h3 {
        font-size: 1rem;
        font-weight: 600;
        color: #333;
    }

    .metric-header .material-icons {
        color: var(--adv-primary);
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--adv-primary);
        margin-bottom: 10px;
    }

    .metric-trend {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
    }

    .metric-trend.positive {
        color: #4CAF50;
    }

    /* Actions ADV */
    .adv-actions-section {
        margin: 80px 0;
    }

    .adv-actions-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .adv-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
    }

    .adv-action-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        padding: 25px 20px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 500;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .adv-action-btn.primary {
        background: var(--adv-primary);
        color: white;
    }

    .adv-action-btn.secondary {
        background: var(--adv-secondary);
        color: white;
    }

    .adv-action-btn.accent {
        background: var(--adv-accent);
        color: white;
    }

    .adv-action-btn.info {
        background: #17A2B8;
        color: white;
    }

    .adv-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0,0,0,0.15);
    }

    .adv-action-btn .material-icons {
        font-size: 2rem;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .table-header,
        .table-row {
            grid-template-columns: 1fr;
        }
        
        .header-cell,
        .table-cell {
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        .header-cell:before,
        .table-cell:before {
            content: attr(data-label);
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
    }
    </style>

    <script>
    // Fonctions pour les actions ADV
    function viewTicket(ticketId) {
        alert('Affichage du ticket #' + ticketId);
    }

    function editTicket(ticketId) {
        alert('Édition du ticket #' + ticketId);
    }

    function loadMoreTickets() {
        alert('Chargement de plus de tickets...');
    }

    function createTicket() {
        alert('Création d\'un nouveau ticket...');
    }

    function processReturn() {
        alert('Traitement d\'un retour...');
    }

    function generateReport() {
        alert('Génération du rapport mensuel...');
    }

    function viewKnowledgeBase() {
        alert('Ouverture de la base de connaissances...');
    }
    </script>

<?php require_once 'bottom_bar.php'; ?>
</body>
</html>
