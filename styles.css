/**
 * Styles CSS pour les pages RSE et ADV du portail Schluter Systems
 */

/* Variables CSS */
:root {
    --schluter-primary: #E85A2B;
    --schluter-secondary: #2B4C85;
    --schluter-accent: #F4A261;
    --schluter-background: #F8F9FA;
    --schluter-text: #333333;
    --schluter-white: #FFFFFF;
    --schluter-light-gray: #E9ECEF;
    --schluter-dark-gray: #6C757D;
    --schluter-shadow: rgba(0, 0, 0, 0.1);
    --schluter-border-radius: 8px;
    --schluter-transition: all 0.3s ease;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--schluter-background);
    color: var(--schluter-text);
    line-height: 1.6;
}

/* Conteneur principal */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Titres de section */
.section-title {
    text-align: center;
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--schluter-text);
    margin-bottom: 40px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--schluter-primary);
}

/* Bannières */
.schluter-banner {
    background: linear-gradient(135deg, var(--schluter-primary), var(--schluter-secondary));
    color: var(--schluter-white);
    padding: 60px 0;
    text-align: center;
}

.banner-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.banner-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Grilles de statistiques */
.stats-section {
    margin: 50px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: var(--schluter-white);
    padding: 25px;
    border-radius: var(--schluter-border-radius);
    text-align: center;
    box-shadow: 0 2px 10px var(--schluter-shadow);
    transition: var(--schluter-transition);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--schluter-shadow);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--schluter-primary);
    margin-bottom: 10px;
}

.stat-label {
    color: var(--schluter-dark-gray);
    font-weight: 500;
}

/* Grilles de services */
.services-section {
    margin: 80px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.service-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 30px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
    transition: var(--schluter-transition);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--schluter-shadow);
}

.card-icon {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.card-icon .material-icons {
    font-size: 28px;
}

.card-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--schluter-text);
}

.card-description {
    color: var(--schluter-dark-gray);
    margin-bottom: 20px;
    line-height: 1.6;
}

.card-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--schluter-primary);
    color: var(--schluter-white);
    text-decoration: none;
    padding: 12px 24px;
    border-radius: var(--schluter-border-radius);
    font-weight: 500;
    transition: var(--schluter-transition);
}

.card-link:hover {
    background: var(--schluter-secondary);
    transform: translateY(-2px);
}

/* Boutons d'action */
.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 25px 20px;
    border: none;
    border-radius: var(--schluter-border-radius);
    cursor: pointer;
    transition: var(--schluter-transition);
    font-weight: 500;
    box-shadow: 0 2px 10px var(--schluter-shadow);
    text-decoration: none;
    color: inherit;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--schluter-shadow);
}

.action-btn .material-icons {
    font-size: 2rem;
}

/* Variantes de couleurs pour les boutons */
.action-btn.primary {
    background: var(--schluter-primary);
    color: var(--schluter-white);
}

.action-btn.secondary {
    background: var(--schluter-secondary);
    color: var(--schluter-white);
}

.action-btn.accent {
    background: var(--schluter-accent);
    color: var(--schluter-white);
}

.action-btn.success {
    background: #4CAF50;
    color: var(--schluter-white);
}

.action-btn.info {
    background: #2196F3;
    color: var(--schluter-white);
}

.action-btn.warning {
    background: #FF9800;
    color: var(--schluter-white);
}

/* Grilles d'actions */
.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

/* Sections avec arrière-plan */
.section-with-background {
    background: var(--schluter-light-gray);
    padding: 60px 0;
    margin: 80px 0;
}

.section-with-background .section-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Cartes d'information */
.info-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 25px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
    margin-bottom: 20px;
    transition: var(--schluter-transition);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px var(--schluter-shadow);
}

.info-card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.info-card-icon {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--schluter-text);
}

.info-card-content {
    color: var(--schluter-dark-gray);
    line-height: 1.6;
}

/* Listes stylisées */
.styled-list {
    list-style: none;
    padding: 0;
}

.styled-list li {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 0;
    border-bottom: 1px solid var(--schluter-light-gray);
}

.styled-list li:last-child {
    border-bottom: none;
}

.styled-list .material-icons {
    color: var(--schluter-primary);
    font-size: 20px;
}

/* Badges et étiquettes */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.badge.primary {
    background: rgba(232, 90, 43, 0.1);
    color: var(--schluter-primary);
}

.badge.success {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.badge.warning {
    background: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.badge.info {
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

/* Barres de progression */
.progress-container {
    margin: 20px 0;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--schluter-light-gray);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--schluter-primary);
    transition: width 0.6s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-title {
        font-size: 2rem;
    }
    
    .banner-subtitle {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .main-content {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .banner-title {
        font-size: 1.5rem;
    }
    
    .stat-value {
        font-size: 2rem;
    }
    
    .card-title {
        font-size: 1.2rem;
    }
    
    .service-card,
    .info-card {
        padding: 20px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Utilitaires */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-20 {
    margin-bottom: 20px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mb-40 {
    margin-bottom: 40px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.mt-40 {
    margin-top: 40px;
}
