<?php
/**
 * Pied de page commun pour les pages RSE et ADV
 */
?>

<footer class="schluter-footer">
    <div class="footer-container">
        <div class="footer-content">
            <div class="footer-section">
                <h4>Schluter Systems</h4>
                <p>Portail intranet pour la gestion des ressources internes</p>
                <div class="footer-logo">
                    <img src="images/schluter-logo-white.png" alt="Schluter Systems" class="footer-logo-img">
                </div>
            </div>
            
            <div class="footer-section">
                <h4>Support</h4>
                <div class="footer-links">
                    <p><i class="material-icons">phone</i> IT Helpdesk: ext. 1234</p>
                    <p><i class="material-icons">email</i> <EMAIL></p>
                    <p><i class="material-icons">location_on</i> Siège social: Paris</p>
                </div>
            </div>
            
            <div class="footer-section">
                <h4>Liens Rapides</h4>
                <div class="footer-links">
                    <a href="assistance.php"><i class="material-icons">help_center</i> Assistance</a>
                    <a href="dashboard.php"><i class="material-icons">dashboard</i> Tableau de Bord</a>
                    <a href="index.php"><i class="material-icons">home</i> Accueil</a>
                    <a href="IT.php"><i class="material-icons">computer</i> Informatique</a>
                </div>
            </div>
            
            <div class="footer-section">
                <h4>Ressources</h4>
                <div class="footer-links">
                    <a href="documentation.php"><i class="material-icons">library_books</i> Documentation</a>
                    <a href="formations.php"><i class="material-icons">school</i> Formations</a>
                    <a href="politique.php"><i class="material-icons">policy</i> Politiques</a>
                    <a href="contact.php"><i class="material-icons">contact_support</i> Contact</a>
                </div>
            </div>
        </div>
        
        <div class="footer-bottom">
            <div class="footer-bottom-content">
                <p>&copy; <?php echo date('Y'); ?> Schluter Systems. Tous droits réservés.</p>
                <div class="footer-bottom-links">
                    <a href="mentions-legales.php">Mentions légales</a>
                    <a href="confidentialite.php">Confidentialité</a>
                    <a href="cookies.php">Cookies</a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- Scripts communs -->
<script src="js/schluter-common.js"></script>

<style>
/* Styles pour le pied de page commun */
.schluter-footer {
    background: #2B4C85;
    color: white;
    margin-top: 80px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    margin-bottom: 20px;
    color: #F4A261;
    font-size: 1.2rem;
    font-weight: 600;
}

.footer-section p {
    margin-bottom: 10px;
    line-height: 1.6;
    opacity: 0.9;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.footer-links a {
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    opacity: 0.9;
    padding: 5px 0;
}

.footer-links a:hover {
    color: #F4A261;
    opacity: 1;
    transform: translateX(5px);
}

.footer-links p {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.footer-links .material-icons {
    font-size: 18px;
    opacity: 0.8;
}

.footer-logo {
    margin-top: 20px;
}

.footer-logo-img {
    height: 35px;
    width: auto;
    opacity: 0.8;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-bottom p {
    opacity: 0.8;
    margin: 0;
}

.footer-bottom-links {
    display: flex;
    gap: 20px;
}

.footer-bottom-links a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.footer-bottom-links a:hover {
    opacity: 1;
    color: #F4A261;
}

/* Animation d'apparition du footer */
.schluter-footer {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .footer-section h4 {
        font-size: 1.1rem;
    }
    
    .footer-links a {
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .footer-container {
        padding: 30px 15px 15px;
    }
    
    .footer-bottom-links {
        flex-direction: column;
        gap: 10px;
    }
}

/* Effet de survol pour les sections du footer */
.footer-section {
    transition: all 0.3s ease;
    padding: 15px;
    border-radius: 8px;
}

.footer-section:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

/* Style pour les icônes Material */
.footer-links .material-icons {
    transition: all 0.3s ease;
}

.footer-links a:hover .material-icons {
    color: #F4A261;
    transform: scale(1.1);
}
</style>
