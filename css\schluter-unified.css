/**
 * Styles unifiés pour le portail intranet Schluter Systems
 */

/* Variables CSS pour les couleurs Schluter */
:root {
    --schluter-primary: #E85A2B;
    --schluter-secondary: #2B4C85;
    --schluter-accent: #F4A261;
    --schluter-background: #F8F9FA;
    --schluter-text: #333333;
    --schluter-white: #FFFFFF;
    --schluter-light-gray: #E9ECEF;
    --schluter-dark-gray: #6C757D;
    --schluter-shadow: rgba(0, 0, 0, 0.1);
    --schluter-border-radius: 8px;
    --schluter-transition: all 0.3s ease;
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--schluter-background);
    color: var(--schluter-text);
    line-height: 1.6;
}

/* Navigation principale */
.schluter-nav {
    background: var(--schluter-white);
    box-shadow: 0 2px 10px var(--schluter-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    height: 40px;
    width: auto;
}

.brand-text {
    font-weight: 600;
    font-size: 1.2rem;
    color: var(--schluter-primary);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: var(--schluter-text);
    padding: 10px 15px;
    border-radius: var(--schluter-border-radius);
    transition: var(--schluter-transition);
    font-weight: 500;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background-color: var(--schluter-primary);
    color: var(--schluter-white);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--schluter-dark-gray);
}

/* Sous-navigation */
.schluter-subnav {
    background: var(--schluter-light-gray);
    border-bottom: 1px solid #ddd;
}

.subnav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.subnav-menu {
    display: flex;
    list-style: none;
    gap: 20px;
}

.subnav-link {
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
    color: var(--schluter-text);
    padding: 12px 16px;
    border-radius: var(--schluter-border-radius);
    transition: var(--schluter-transition);
    font-size: 0.9rem;
}

.subnav-link:hover {
    background-color: var(--schluter-white);
    color: var(--schluter-primary);
}

/* Bannières */
.schluter-banner {
    background: linear-gradient(135deg, var(--schluter-primary), var(--schluter-secondary));
    color: var(--schluter-white);
    padding: 60px 0;
    text-align: center;
}

.schluter-banner.rse {
    background: linear-gradient(135deg, #4CAF50, #2E7D32);
}

.schluter-banner.adv {
    background: linear-gradient(135deg, var(--schluter-secondary), #1565C0);
}

.banner-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.banner-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 15px;
}

.banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Cartes de service */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    max-width: 1200px;
    margin: 50px auto;
    padding: 0 20px;
}

.service-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 30px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
    transition: var(--schluter-transition);
    text-align: center;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--schluter-shadow);
}

.card-icon {
    background: var(--schluter-primary);
    color: var(--schluter-white);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
}

.card-icon .material-icons {
    font-size: 28px;
}

.card-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--schluter-text);
}

.card-description {
    color: var(--schluter-dark-gray);
    margin-bottom: 20px;
    line-height: 1.6;
}

.card-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: var(--schluter-primary);
    color: var(--schluter-white);
    text-decoration: none;
    padding: 12px 24px;
    border-radius: var(--schluter-border-radius);
    font-weight: 500;
    transition: var(--schluter-transition);
}

.card-link:hover {
    background: var(--schluter-secondary);
}

/* Statistiques */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 20px;
}

.stat-card {
    background: var(--schluter-white);
    padding: 25px;
    border-radius: var(--schluter-border-radius);
    text-align: center;
    box-shadow: 0 2px 10px var(--schluter-shadow);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--schluter-primary);
    margin-bottom: 10px;
}

.stat-label {
    color: var(--schluter-dark-gray);
    font-weight: 500;
}

/* Pied de page */
.schluter-footer {
    background: var(--schluter-secondary);
    color: var(--schluter-white);
    margin-top: 80px;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 20px 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.footer-section h4 {
    margin-bottom: 15px;
    color: var(--schluter-accent);
}

.footer-section a {
    color: var(--schluter-white);
    text-decoration: none;
    display: block;
    margin-bottom: 8px;
    transition: var(--schluter-transition);
}

.footer-section a:hover {
    color: var(--schluter-accent);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 20px;
    text-align: center;
    opacity: 0.8;
}

/* Responsive */
@media (max-width: 768px) {
    .nav-container {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
    }
    
    .nav-menu {
        margin-top: 15px;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .banner-title {
        font-size: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        margin: 30px auto;
    }
}
