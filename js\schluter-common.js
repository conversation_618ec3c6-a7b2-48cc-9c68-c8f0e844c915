/**
 * JavaScript commun pour le portail intranet Schluter Systems
 */

// Configuration globale
const SchlutterPortal = {
    config: {
        animationDuration: 300,
        apiEndpoint: '/api/',
        version: '1.0.0'
    },
    
    // Initialisation du portail
    init: function() {
        this.setupNavigation();
        this.setupServiceCards();
        this.setupResponsiveMenu();
        this.setupTooltips();
        console.log('Schluter Portal initialized v' + this.config.version);
    },
    
    // Configuration de la navigation
    setupNavigation: function() {
        // Gestion des liens actifs
        const currentPage = window.location.pathname.split('/').pop();
        const navLinks = document.querySelectorAll('.nav-link, .subnav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href === currentPage) {
                link.closest('.nav-item, .subnav-item').classList.add('active');
            }
        });
        
        // Animation des liens de navigation
        navLinks.forEach(link => {
            link.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            link.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    },
    
    // Configuration des cartes de service
    setupServiceCards: function() {
        const serviceCards = document.querySelectorAll('.service-card');
        
        serviceCards.forEach(card => {
            // Animation au survol
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
            
            // Animation au clic
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.card-link')) {
                    const link = this.querySelector('.card-link');
                    if (link) {
                        window.location.href = link.getAttribute('href');
                    }
                }
            });
        });
    },
    
    // Menu responsive
    setupResponsiveMenu: function() {
        // Création du bouton menu mobile si nécessaire
        const nav = document.querySelector('.schluter-nav');
        if (nav && window.innerWidth <= 768) {
            this.createMobileMenu();
        }
        
        // Gestion du redimensionnement
        window.addEventListener('resize', () => {
            if (window.innerWidth <= 768) {
                this.createMobileMenu();
            } else {
                this.removeMobileMenu();
            }
        });
    },
    
    // Création du menu mobile
    createMobileMenu: function() {
        const navContainer = document.querySelector('.nav-container');
        if (!navContainer || navContainer.querySelector('.mobile-menu-toggle')) return;
        
        const mobileToggle = document.createElement('button');
        mobileToggle.className = 'mobile-menu-toggle';
        mobileToggle.innerHTML = '<i class="material-icons">menu</i>';
        mobileToggle.addEventListener('click', this.toggleMobileMenu);
        
        navContainer.appendChild(mobileToggle);
    },
    
    // Suppression du menu mobile
    removeMobileMenu: function() {
        const mobileToggle = document.querySelector('.mobile-menu-toggle');
        if (mobileToggle) {
            mobileToggle.remove();
        }
    },
    
    // Basculement du menu mobile
    toggleMobileMenu: function() {
        const navMenu = document.querySelector('.nav-menu');
        if (navMenu) {
            navMenu.classList.toggle('mobile-open');
        }
    },
    
    // Configuration des tooltips
    setupTooltips: function() {
        const elementsWithTooltip = document.querySelectorAll('[data-tooltip]');
        
        elementsWithTooltip.forEach(element => {
            element.addEventListener('mouseenter', function(e) {
                SchlutterPortal.showTooltip(e.target, e.target.getAttribute('data-tooltip'));
            });
            
            element.addEventListener('mouseleave', function() {
                SchlutterPortal.hideTooltip();
            });
        });
    },
    
    // Affichage des tooltips
    showTooltip: function(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'schluter-tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
        
        setTimeout(() => tooltip.classList.add('visible'), 10);
    },
    
    // Masquage des tooltips
    hideTooltip: function() {
        const tooltip = document.querySelector('.schluter-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },
    
    // Utilitaires
    utils: {
        // Animation de compteur pour les statistiques
        animateCounter: function(element, target, duration = 2000) {
            const start = 0;
            const increment = target / (duration / 16);
            let current = start;
            
            const timer = setInterval(() => {
                current += increment;
                element.textContent = Math.floor(current);
                
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                }
            }, 16);
        },
        
        // Notification toast
        showNotification: function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `schluter-notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('visible'), 10);
            setTimeout(() => {
                notification.classList.remove('visible');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        },
        
        // Formatage des dates
        formatDate: function(date) {
            return new Intl.DateTimeFormat('fr-FR', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }).format(new Date(date));
        }
    }
};

// Styles CSS pour les fonctionnalités JavaScript
const dynamicStyles = `
    .mobile-menu-toggle {
        display: none;
        background: none;
        border: none;
        color: var(--schluter-primary);
        font-size: 1.5rem;
        cursor: pointer;
    }
    
    @media (max-width: 768px) {
        .mobile-menu-toggle {
            display: block;
        }
        
        .nav-menu.mobile-open {
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            padding: 20px;
        }
    }
    
    .schluter-tooltip {
        position: absolute;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 0.8rem;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 1000;
        pointer-events: none;
    }
    
    .schluter-tooltip.visible {
        opacity: 1;
    }
    
    .schluter-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        z-index: 1000;
    }
    
    .schluter-notification.visible {
        transform: translateX(0);
    }
    
    .schluter-notification.info {
        background: var(--schluter-primary);
    }
    
    .schluter-notification.success {
        background: #4CAF50;
    }
    
    .schluter-notification.error {
        background: #f44336;
    }
`;

// Injection des styles dynamiques
const styleSheet = document.createElement('style');
styleSheet.textContent = dynamicStyles;
document.head.appendChild(styleSheet);

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    SchlutterPortal.init();
});

// Export pour utilisation dans d'autres scripts
window.SchlutterPortal = SchlutterPortal;
