<?php
/**
 * Configuration principale du portail intranet Schluter Systems
 */

// Configuration de base
define('SITE_TITLE', 'Portail Intranet - Schluter Systems');
define('COMPANY_NAME', 'Schluter Systems');
define('BASE_URL', '/');

// Configuration de la navigation principale
$navigation_config = [
    'accueil' => [
        'title' => 'Accueil',
        'url' => 'index.php',
        'icon' => 'home',
        'active' => true
    ],
    'informatique' => [
        'title' => 'Informatique',
        'url' => 'IT.php',
        'icon' => 'computer',
        'sub_modules' => [
            'inventaire' => [
                'title' => 'Inventaire',
                'url' => 'inventaire.php',
                'icon' => 'inventory'
            ],
            'accuse_reception' => [
                'title' => 'Accusés de Réception',
                'url' => 'accuse_reception.php',
                'icon' => 'receipt'
            ],
            'end_of_life' => [
                'title' => 'Changements de Poste',
                'url' => 'End_Of_Life.php',
                'icon' => 'refresh'
            ]
        ]
    ],
    'rse' => [
        'title' => 'RSE',
        'url' => 'RSE.php',
        'icon' => 'eco',
        'sub_modules' => [
            'evaluation' => [
                'title' => 'Évaluation des Prestataires',
                'url' => 'evaluation.php',
                'icon' => 'assessment'
            ],
            'engagement' => [
                'title' => 'Engagement Communautaire',
                'url' => 'engagement_communautaire.php',
                'icon' => 'community'
            ],
            'rapports' => [
                'title' => 'Rapports RSE',
                'url' => 'rapports_rse.php',
                'icon' => 'report'
            ]
        ]
    ],
    'adv' => [
        'title' => 'ADV',
        'url' => 'ADV.php',
        'icon' => 'support',
        'sub_modules' => [
            'support_client' => [
                'title' => 'Support Client',
                'url' => 'support_client.php',
                'icon' => 'help'
            ],
            'gestion_retours' => [
                'title' => 'Gestion des Retours',
                'url' => 'gestion_retours.php',
                'icon' => 'return'
            ]
        ]
    ],
    'assistance' => [
        'title' => 'Assistance',
        'url' => 'assistance.php',
        'icon' => 'help_center'
    ],
    'tableau_bord' => [
        'title' => 'Tableau de Bord',
        'url' => 'dashboard.php',
        'icon' => 'dashboard'
    ]
];

// Configuration des couleurs Schluter
$schluter_colors = [
    'primary' => '#E85A2B',      // Orange Schluter
    'secondary' => '#2B4C85',    // Bleu foncé
    'accent' => '#F4A261',       // Orange clair
    'background' => '#F8F9FA',   // Gris très clair
    'text' => '#333333',         // Gris foncé
    'white' => '#FFFFFF',
    'light_gray' => '#E9ECEF',
    'dark_gray' => '#6C757D'
];

// Configuration des services
function loadServices() {
    return [
        'it_services' => [
            'inventaire' => 'Gestion de l\'inventaire informatique',
            'support' => 'Support technique interne',
            'maintenance' => 'Maintenance préventive'
        ],
        'rse_services' => [
            'evaluation' => 'Évaluation des prestataires',
            'engagement' => 'Projets communautaires',
            'reporting' => 'Rapports de durabilité'
        ],
        'adv_services' => [
            'support' => 'Support client après-vente',
            'retours' => 'Gestion des retours produits'
        ]
    ];
}

// Configuration de la base de données (à adapter selon votre environnement)
$db_config = [
    'host' => 'localhost',
    'dbname' => 'schluter_intranet',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

// Fonction utilitaire pour obtenir l'URL actuelle
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF']);
}

// Fonction pour vérifier si une page est active
function isActivePage($page_url) {
    return getCurrentPage() === $page_url;
}
?>
