<?php
/**
 * Page d'accueil du portail intranet Schluter Systems
 */

require_once 'templates/components.php';

// Chargement des services
$services = loadServices();

// Statistiques pour la page d'accueil
$stats = [
    ['value' => '150+', 'label' => 'Employés'],
    ['value' => '25', 'label' => 'Départements'],
    ['value' => '500+', 'label' => 'Équipements IT'],
    ['value' => '99.9%', 'label' => 'Disponibilité']
];

// Génération de l'en-tête
renderSchlutterHeader('Accueil');

// Navigation principale
renderSchlutterNavigation('accueil');

// Bannière principale
renderSchlutterBanner(
    'Bienvenue sur le Portail Intranet',
    'Votre hub central pour accéder à toutes les ressources et services internes de Schluter Systems'
);
?>

<main class="main-content">
    <!-- Section des statistiques -->
    <section class="stats-section">
        <?php renderStatsGrid($stats); ?>
    </section>

    <!-- Section des services principaux -->
    <section class="services-section">
        <div class="services-grid">
            <?php
            renderServiceCard(
                'Informatique',
                'Accédez aux outils de gestion informatique, inventaire, support technique et maintenance préventive.',
                'IT.php',
                'computer'
            );
            
            renderServiceCard(
                'RSE',
                'Gérez les initiatives de responsabilité sociétale, évaluations des prestataires et rapports de durabilité.',
                'RSE.php',
                'eco'
            );
            
            renderServiceCard(
                'ADV',
                'Outils de gestion du service après-vente, support client et gestion des retours produits.',
                'ADV.php',
                'support'
            );
            
            renderServiceCard(
                'Assistance',
                'Centre d\'aide et support pour toutes vos questions concernant le portail intranet.',
                'assistance.php',
                'help_center'
            );
            
            renderServiceCard(
                'Tableau de Bord',
                'Vue d\'ensemble des indicateurs clés et métriques de performance de l\'entreprise.',
                'dashboard.php',
                'dashboard'
            );
            
            renderServiceCard(
                'Ressources Humaines',
                'Gestion des ressources humaines, congés, formations et évaluations du personnel.',
                'rh.php',
                'people'
            );
            ?>
        </div>
    </section>

    <!-- Section des actualités -->
    <section class="news-section">
        <div class="news-container">
            <h2 class="section-title">Actualités Récentes</h2>
            <div class="news-grid">
                <article class="news-card">
                    <div class="news-date">15 Juin 2025</div>
                    <h3 class="news-title">Mise à jour du système informatique</h3>
                    <p class="news-excerpt">Nouvelle version du portail intranet avec des fonctionnalités améliorées...</p>
                    <a href="#" class="news-link">Lire la suite</a>
                </article>
                
                <article class="news-card">
                    <div class="news-date">12 Juin 2025</div>
                    <h3 class="news-title">Initiative RSE Q2 2025</h3>
                    <p class="news-excerpt">Lancement de nouveaux projets de développement durable...</p>
                    <a href="#" class="news-link">Lire la suite</a>
                </article>
                
                <article class="news-card">
                    <div class="news-date">10 Juin 2025</div>
                    <h3 class="news-title">Formation sécurité informatique</h3>
                    <p class="news-excerpt">Sessions de formation obligatoires sur la cybersécurité...</p>
                    <a href="#" class="news-link">Lire la suite</a>
                </article>
            </div>
        </div>
    </section>

    <!-- Section des liens rapides -->
    <section class="quick-links-section">
        <div class="quick-links-container">
            <h2 class="section-title">Liens Rapides</h2>
            <div class="quick-links-grid">
                <a href="inventaire.php" class="quick-link">
                    <i class="material-icons">inventory</i>
                    <span>Inventaire IT</span>
                </a>
                <a href="support_client.php" class="quick-link">
                    <i class="material-icons">help</i>
                    <span>Support Client</span>
                </a>
                <a href="evaluation.php" class="quick-link">
                    <i class="material-icons">assessment</i>
                    <span>Évaluations</span>
                </a>
                <a href="rapports_rse.php" class="quick-link">
                    <i class="material-icons">report</i>
                    <span>Rapports RSE</span>
                </a>
            </div>
        </div>
    </section>
</main>

<style>
/* Styles spécifiques à la page d'accueil */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-title {
    text-align: center;
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--schluter-text);
    margin-bottom: 40px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--schluter-primary);
}

/* Section actualités */
.news-section {
    margin: 80px 0;
}

.news-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.news-card {
    background: var(--schluter-white);
    border-radius: var(--schluter-border-radius);
    padding: 25px;
    box-shadow: 0 4px 15px var(--schluter-shadow);
    transition: var(--schluter-transition);
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--schluter-shadow);
}

.news-date {
    color: var(--schluter-primary);
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 10px;
}

.news-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--schluter-text);
}

.news-excerpt {
    color: var(--schluter-dark-gray);
    line-height: 1.6;
    margin-bottom: 20px;
}

.news-link {
    color: var(--schluter-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--schluter-transition);
}

.news-link:hover {
    color: var(--schluter-secondary);
}

/* Section liens rapides */
.quick-links-section {
    margin: 80px 0;
    background: var(--schluter-light-gray);
    padding: 60px 0;
}

.quick-links-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.quick-links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.quick-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    background: var(--schluter-white);
    padding: 30px 20px;
    border-radius: var(--schluter-border-radius);
    text-decoration: none;
    color: var(--schluter-text);
    transition: var(--schluter-transition);
    box-shadow: 0 2px 10px var(--schluter-shadow);
}

.quick-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px var(--schluter-shadow);
    color: var(--schluter-primary);
}

.quick-link .material-icons {
    font-size: 2.5rem;
    color: var(--schluter-primary);
}

.quick-link span {
    font-weight: 500;
    text-align: center;
}
</style>

<?php
// Pied de page
renderSchlutterFooter();
?>
